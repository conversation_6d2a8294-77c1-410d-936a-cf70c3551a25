@model List<KanvasKutuphane.Entities.Models.Borrowing>

@{
    ViewData["Title"] = "Ödün<PERSON>leri";
}

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON> Alınan Kitaplar</h2>

<a class="bg-green-600 text-white px-4 py-2 rounded mb-4 inline-block" asp-action="Create"><PERSON><PERSON></a>

<table class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="px-4 py-2 border-b">Üye</th>
            <th class="px-4 py-2 border-b">Kitap</th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON><PERSON></th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON><PERSON></th>
            <th class="px-4 py-2 border-b"><PERSON>ade Tarihi</th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="px-4 py-2 border-b">@item.Member.FirstName @item.Member.LastName</td>
                <td class="px-4 py-2 border-b">@item.Book.Title</td>
                <td class="px-4 py-2 border-b">@item.BorrowDate.ToShortDateString()</td>
                <td class="px-4 py-2 border-b">@(item.DueDate.HasValue ? item.DueDate.Value.ToShortDateString() : "Belirtilmemiş")</td>
                <td class="px-4 py-2 border-b">
                    @(item.ReturnDate.HasValue ? item.ReturnDate.Value.ToShortDateString() : "Henüz İade Edilmedi")
                </td>
                <td class="px-4 py-2 border-b">
                    <a asp-action="Edit" asp-route-id="@item.Id" class="text-blue-600 hover:underline mr-2">Düzenle</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="text-red-600 hover:underline">Sil</a>
                </td>
            </tr>
        }
    </tbody>
</table>
