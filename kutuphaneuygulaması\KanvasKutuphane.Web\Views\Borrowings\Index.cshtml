@model List<KanvasKutuphane.Entities.Models.Borrowing>

@{
    ViewData["Title"] = "Ödünç <PERSON>leri";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-exchange-alt me-2 text-warning"></i><PERSON><PERSON><PERSON><PERSON><PERSON>
                </h1>
                <p class="text-muted mb-0">Kitap ödünç verme ve iade alma işlemlerini yönetin</p>
            </div>
            @if (User.IsInRole("Admin"))
            {
                <div>
                    <a asp-action="Create" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus me-2"></i>Yeni <PERSON><PERSON>n<PERSON>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Filter and Search Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" id="searchInput" placeholder="Üye veya kitap ara...">
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex gap-2">
            <select class="form-select" id="statusFilter">
                <option value="">Tüm İşlemler</option>
                <option value="active">Aktif Ödünçler</option>
                <option value="returned">İade Edilenler</option>
                <option value="overdue">Geciken İadeler</option>
            </select>
            <button class="btn btn-outline-primary" onclick="exportBorrowings()">
                <i class="fas fa-download me-1"></i>Dışa Aktar
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                <h5 class="card-title">Toplam İşlem</h5>
                <h3 class="mb-0">@Model.Count</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h5 class="card-title">Aktif Ödünç</h5>
                <h3 class="mb-0">@Model.Count(b => !b.ReturnDate.HasValue)</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h5 class="card-title">İade Edildi</h5>
                <h3 class="mb-0">@Model.Count(b => b.ReturnDate.HasValue)</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h5 class="card-title">Geciken İade</h5>
                <h3 class="mb-0">@Model.Count(b => !b.ReturnDate.HasValue && b.DueDate.HasValue && b.DueDate.Value < DateTime.Now)</h3>
            </div>
        </div>
    </div>
</div>

<!-- Borrowings Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Ödünç İşlemleri Listesi
                </h5>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0" id="borrowingsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">
                                        <i class="fas fa-id-card me-1"></i>ID
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-user me-1"></i>Üye
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-book me-1"></i>Kitap
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-calendar-plus me-1"></i>Alış Tarihi
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-calendar-check me-1"></i>Teslim Tarihi
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-calendar-times me-1"></i>İade Tarihi
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-info-circle me-1"></i>Durum
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-cogs me-1"></i>İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderByDescending(b => b.BorrowDate))
                                {
                                    var isOverdue = !item.ReturnDate.HasValue && item.DueDate.HasValue && item.DueDate.Value < DateTime.Now;
                                    var isReturned = item.ReturnDate.HasValue;
                                    var rowClass = isOverdue ? "table-danger" : isReturned ? "table-success" : "";

                                    <tr class="@rowClass">
                                        <td class="fw-bold text-primary">@item.Id</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-user-circle me-2 text-success"></i>
                                                <div>
                                                    <strong>@item.Member.FirstName @item.Member.LastName</strong>
                                                    <br><small class="text-muted">@item.Member.Email</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-book me-2 text-primary"></i>
                                                <div>
                                                    <strong>@item.Book.Title</strong>
                                                    <br><small class="text-muted">@item.Book.Author</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted">
                                                @item.BorrowDate.ToString("dd.MM.yyyy")
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            @if (item.DueDate.HasValue)
                                            {
                                                <span class="text-muted">
                                                    @item.DueDate.Value.ToString("dd.MM.yyyy")
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Belirtilmemiş</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            @if (item.ReturnDate.HasValue)
                                            {
                                                <span class="text-success">
                                                    @item.ReturnDate.Value.ToString("dd.MM.yyyy")
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-warning">Henüz İade Edilmedi</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            @if (isReturned)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>İade Edildi
                                                </span>
                                            }
                                            else if (isOverdue)
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Gecikmiş
                                                </span>
                                                <br><small class="text-danger">@((DateTime.Now - item.DueDate.Value).Days) gün</small>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>Aktif
                                                </span>
                                                @if (item.DueDate.HasValue)
                                                {
                                                    <br><small class="text-muted">@((item.DueDate.Value - DateTime.Now).Days) gün kaldı</small>
                                                }
                                            }
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                @if (User.IsInRole("Admin"))
                                                {
                                                    @if (!isReturned)
                                                    {
                                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                                onclick="markAsReturned(@item.Id)" title="İade Edildi Olarak İşaretle">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                    <a asp-action="Edit" asp-route-id="@item.Id"
                                                       class="btn btn-outline-warning btn-sm" title="Düzenle">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id"
                                                       class="btn btn-outline-danger btn-sm" title="Sil"
                                                       onclick="return confirm('Bu ödünç işlemini silmek istediğinizden emin misiniz?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz ödünç işlemi bulunmuyor</h5>
                        <p class="text-muted">İlk ödünç işlemini başlatmak için aşağıdaki butona tıklayın.</p>
                        @if (User.IsInRole("Admin"))
                        {
                            <a asp-action="Create" class="btn btn-warning btn-lg">
                                <i class="fas fa-plus me-2"></i>İlk Ödünç İşlemini Başlat
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Search and filter functionality
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const table = document.getElementById('borrowingsTable');

            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedStatus = statusFilter.value;

                if (table) {
                    const rows = table.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const member = row.cells[1].textContent.toLowerCase();
                        const book = row.cells[2].textContent.toLowerCase();
                        const status = row.cells[6].textContent.toLowerCase();

                        const matchesSearch = member.includes(searchTerm) || book.includes(searchTerm);
                        const matchesStatus = !selectedStatus ||
                                            (selectedStatus === 'active' && status.includes('aktif')) ||
                                            (selectedStatus === 'returned' && status.includes('iade edildi')) ||
                                            (selectedStatus === 'overdue' && status.includes('gecikmiş'));

                        if (matchesSearch && matchesStatus) {
                            row.style.display = '';
                            row.classList.add('fade-in');
                        } else {
                            row.style.display = 'none';
                            row.classList.remove('fade-in');
                        }
                    });
                }
            }

            searchInput.addEventListener('input', filterTable);
            statusFilter.addEventListener('change', filterTable);

            // Add loading animation to table rows
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach((row, index) => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        row.style.transition = 'all 0.4s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, index * 50);
                });
            }

            console.log('Ödünç işlemleri listesi yüklendi!');
        });

        function markAsReturned(borrowingId) {
            if (confirm('Bu kitap iade edildi olarak işaretlensin mi?')) {
                // Here you would implement the actual return marking logic
                // For now, just show a success message
                alert('Kitap iade edildi olarak işaretlendi.');
                location.reload();
            }
        }

        function exportBorrowings() {
            alert('Ödünç işlemleri dışa aktarma özelliği yakında eklenecek!');
        }
    </script>

    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
}
