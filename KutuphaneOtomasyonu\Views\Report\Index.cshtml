@{
    ViewData["Title"] = "Raporlar";
}

<div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Raporlar</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-blue-600 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">En Popüler Kitaplar</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">En çok ödünç alınan kitapların listesini görüntüleyin.</p>
                <a asp-action="PopularBooks" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <PERSON><PERSON><PERSON>
                </a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-red-600 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">Gecikmiş İadeler</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">İade tarihi geçmiş kitapların listesini görüntüleyin.</p>
                <a asp-action="OverdueLoans" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Raporu Görüntüle
                </a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-green-600 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">Üye Ödünç Geçmişi</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">Belirli bir üyenin ödünç geçmişini görüntüleyin.</p>
                <a asp-action="MemberLoanHistory" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Raporu Görüntüle
                </a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-purple-600 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">Kategori İstatistikleri</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">Kitapların kategorilere göre dağılımını görüntüleyin.</p>
                <a asp-action="BooksByGenre" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Raporu Görüntüle
                </a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-yellow-600 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">Aylık Ödünç İstatistikleri</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">Aylık ödünç verme istatistiklerini görüntüleyin.</p>
                <a asp-action="MonthlyLoanStatistics" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    Raporu Görüntüle
                </a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-800 text-white px-4 py-3">
                <h2 class="text-xl font-semibold">Kategori Yönetimi</h2>
            </div>
            <div class="p-4">
                <p class="text-gray-600 mb-4">Kütüphane kategorilerini ekleyin, düzenleyin veya silin.</p>
                <a asp-action="Categories" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800">
                    Kategori Yönetimine Git
                </a>
            </div>
        </div>
    </div>
</div>
