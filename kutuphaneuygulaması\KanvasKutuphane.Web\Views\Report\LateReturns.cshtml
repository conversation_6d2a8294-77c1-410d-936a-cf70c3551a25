@model List<KanvasKutuphane.Business.ViewModels.LateReturnViewModel>

@{
    ViewData["Title"] = "Geciken İadeler";
}

<h2 class="text-2xl font-semibold mb-4">Geciken İadeler</h2>

<table class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b">Kitap Adı</th>
            <th class="py-2 px-4 border-b"><PERSON>ye Adı</th>
            <th class="py-2 px-4 border-b"><PERSON><PERSON><PERSON>hi</th>
            <th class="py-2 px-4 border-b"><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="py-2 px-4 border-b">@item.BookTitle</td>
                <td class="py-2 px-4 border-b">@item.MemberName</td>
                <td class="py-2 px-4 border-b">@item.DueDate.ToShortDateString()</td>
                <td class="py-2 px-4 border-b text-red-600 font-semibold">@item.DaysLate</td>
            </tr>
        }
    </tbody>
</table>
