@model List<KanvasKutuphane.Business.ViewModels.LateReturnViewModel>

@{
    ViewData["Title"] = "Geciken İadeler";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>Geciken İadeler
                </h1>
                <p class="text-muted mb-0">Teslim tarihi geçmiş kitapların listesi</p>
            </div>
            <div>
                <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Alert Section -->
@if (Model.Any())
{
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-clock me-2"></i>
                <strong>Dikkat!</strong> @Model.Count adet gecikmiş iade bulunmaktadır.
                Lütfen ilgili üyelerle iletişime geçin.
            </div>
        </div>
    </div>
}

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h5 class="card-title">Geciken İade</h5>
                <h3 class="mb-0">@Model.Count</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-times fa-2x mb-2"></i>
                <h5 class="card-title">Ortalama Gecikme</h5>
                <h3 class="mb-0">@(Model.Any() ? Model.Average(x => x.DaysLate).ToString("F0") : "0") gün</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h5 class="card-title">En Uzun Gecikme</h5>
                <h3 class="mb-0">@(Model.Any() ? Model.Max(x => x.DaysLate) : 0) gün</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h5 class="card-title">Etkilenen Üye</h5>
                <h3 class="mb-0">@(Model.Select(x => x.MemberName).Distinct().Count())</h3>
            </div>
        </div>
    </div>
</div>

<!-- Late Returns Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Geciken İadeler Listesi
                </h5>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">
                                        <i class="fas fa-book me-1"></i>Kitap Adı
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-user me-1"></i>Üye Adı
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-calendar me-1"></i>Teslim Tarihi
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-clock me-1"></i>Gecikme
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-cogs me-1"></i>İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderByDescending(x => x.DaysLate))
                                {
                                    var urgencyClass = item.DaysLate > 30 ? "table-danger" : item.DaysLate > 14 ? "table-warning" : "";
                                    var badgeClass = item.DaysLate > 30 ? "bg-danger" : item.DaysLate > 14 ? "bg-warning" : "bg-secondary";

                                    <tr class="@urgencyClass">
                                        <td>
                                            <strong>@item.BookTitle</strong>
                                        </td>
                                        <td>
                                            <i class="fas fa-user-circle me-1"></i>@item.MemberName
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted">
                                                @item.DueDate.ToString("dd.MM.yyyy")
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge @badgeClass fs-6">
                                                @item.DaysLate gün
                                            </span>
                                            @if (item.DaysLate > 30)
                                            {
                                                <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Kritik</small>
                                            }
                                            else if (item.DaysLate > 14)
                                            {
                                                <br><small class="text-warning"><i class="fas fa-exclamation"></i> Acil</small>
                                            }
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                @if (User.IsInRole("Admin"))
                                                {
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                            onclick="sendReminder('@item.MemberName')" title="Hatırlatma Gönder">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                            onclick="markAsReturned('@item.BookTitle')" title="İade Edildi Olarak İşaretle">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">Harika! Geciken iade bulunmuyor</h5>
                        <p class="text-muted">Tüm kitaplar zamanında iade edilmiş.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Additional Info -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>Gecikme Politikası
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-secondary me-2">1-14 gün</span>
                            <small>Normal gecikme - Hatırlatma gönder</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-warning me-2">15-30 gün</span>
                            <small>Acil durum - Telefon ile arayın</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-danger me-2">30+ gün</span>
                            <small>Kritik - Resmi uyarı gönderin</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading animation to table rows
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.4s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 100);
            });

            console.log('Geciken iadeler raporu yüklendi!');
        });

        function sendReminder(memberName) {
            if (confirm(`${memberName} adlı üyeye hatırlatma e-postası göndermek istediğinizden emin misiniz?`)) {
                // Here you would implement the actual reminder sending logic
                alert(`${memberName} adlı üyeye hatırlatma gönderildi.`);
            }
        }

        function markAsReturned(bookTitle) {
            if (confirm(`${bookTitle} kitabı iade edildi olarak işaretlensin mi?`)) {
                // Here you would implement the actual return marking logic
                alert(`${bookTitle} kitabı iade edildi olarak işaretlendi.`);
                location.reload();
            }
        }
    </script>
}
