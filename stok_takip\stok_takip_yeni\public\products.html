<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stok Hareketleri</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    
</head>
<body class="app-body">
    <div class="layout-container">
        <!-- Sidebar -->
        <aside class="sidebar"></aside>

        <!-- Main Content -->
        <main class="main-content">
            <section id="products" class="section">
                <div class="section-header">
                    <h1 class="section-title">Ü<PERSON><PERSON>nler</h1>
                    <button id="add-product-btn" class="btn btn-primary">
                        <i class="fas fa-plus btn-icon"></i> <PERSON><PERSON>
                    </button>
                </div>
                
                <!-- Search and Filter -->
                <div class="card">
                    <div class="flex gap-4">
                        <div class="form-group flex-1">
                            <label for="search-product" class="form-label">Ürün Ara</label>
                            <input type="text" id="search-product" class="form-input" placeholder="Ürün adı veya kodu">
                        </div>
                        
                        <div class="form-group flex-1">
                            <label for="filter-category" class="form-label">Kategori</label>
                            <select id="filter-category" class="form-select">
                                <option value="all">Tüm Kategoriler</option>
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>
                        
                        <div class="form-group flex-1">
                            <label for="filter-stock" class="form-label">Stok Durumu</label>
                            <select id="filter-stock" class="form-select">
                                <option value="">Tümü</option>
                                <option value="in-stock">Stokta</option>
                                <option value="low-stock">Kritik Stok</option>
                                <option value="out-of-stock">Stok Dışı</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Products Table -->
                <div class="card">
                    <table class="table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell">Ürün Kodu</th>
                                <th class="table-cell">Ürün Adı</th>
                                <th class="table-cell">Kategori</th>
                                <th class="table-cell">Stok</th>
                                <th class="table-cell">Birim</th>
                                <th class="table-cell">Durum</th>
                                <th class="table-cell">İşlemler</th>
                            </tr>
                        </thead>
                        <tbody id="products-table">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Product Modal -->
    <div id="add-product-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Yeni Ürün Ekle</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="add-product-form" onsubmit="handleAddProduct(event)">
                    <div class="form-group">
                        <label for="product-name" class="form-label">Ürün Adı</label>
                        <input type="text" id="product-name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="product-category" class="form-label">Kategori</label>
                        <select id="product-category" class="form-select" required>
                            <option value="">Kategori Seçin</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="product-stock" class="form-label">Başlangıç Stok</label>
                            <input type="number" id="product-stock" class="form-input" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="product-unit" class="form-label">Birim</label>
                            <select id="product-unit" class="form-select" required>
                                <option value="adet">Adet</option>
                                <option value="kg">Kg</option>
                                <option value="lt">Lt</option>
                                <option value="mt">Mt</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="product-price" class="form-label">Birim Fiyat</label>
                        <input type="number" id="product-price" class="form-input" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="product-description" class="form-label">Açıklama</label>
                        <textarea id="product-description" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal">İptal</button>
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div id="edit-product-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Ürün Düzenle</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="edit-product-form" onsubmit="handleEditProduct(event)">
                    <input type="hidden" id="edit-product-id">
                    <div class="form-group">
                        <label for="edit-product-code" class="form-label">Ürün Kodu</label>
                        <input type="text" id="edit-product-code" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-product-name" class="form-label">Ürün Adı</label>
                        <input type="text" id="edit-product-name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-product-category" class="form-label">Kategori</label>
                        <select id="edit-product-category" class="form-select" required>
                            <option value="">Kategori Seçin</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="edit-product-unit" class="form-label">Birim</label>
                            <select id="edit-product-unit" class="form-select" required>
                                <option value="adet">Adet</option>
                                <option value="kg">Kg</option>
                                <option value="lt">Lt</option>
                                <option value="mt">Mt</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-product-price" class="form-label">Birim Fiyat</label>
                            <input type="number" id="edit-product-price" class="form-input" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit-product-description" class="form-label">Açıklama</label>
                        <textarea id="edit-product-description" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal">İptal</button>
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-confirmation-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <h3 class="text-xl font-semibold mb-4">Silme Onayı</h3>
                <p class="mb-4 text-red-600 font-medium">Dikkat!</p>
                <p class="mb-4">Bu <span class="delete-confirmation-type">ürünü</span> silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve ürünle ilgili tüm stok hareketleri de silinecektir.</p>
                <div class="flex justify-end gap-2">
                    <button class="btn btn-secondary close-modal">İptal</button>
                    <button class="btn btn-danger" onclick="handleDeleteConfirmation(event)">Evet, Sil</button>
                </div>
                <input type="hidden" class="delete-confirmation-id">
                <input type="hidden" class="delete-confirmation-type">
            </div>
        </div>
    </div>

    <!-- Main JavaScript -->
    <script src="app.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>