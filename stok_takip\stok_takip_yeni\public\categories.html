<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stok Hareketleri</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    
</head>
<body class="app-body">
    <div class="layout-container">
       <!-- Sidebar -->
       <aside class="sidebar"></aside>
        <!-- Main Content -->
        <main class="main-content">
            <section id="categories" class="section">
                <!-- Header -->
                <div class="section-header">
                    <h1 class="section-title">Kategoriler</h1>
                    <button id="add-category-btn" class="btn btn-primary">
                        <i class="fas fa-plus btn-icon"></i> Yeni <PERSON>
                    </button>
                </div>
                
                <!-- Categories Table -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-3 px-4 text-left font-semibold">Kategori ID</th>
                                    <th class="py-3 px-4 text-left font-semibold">Kategori Adı</th>
                                    <th class="py-3 px-4 text-left font-semibold">Açıklama</th>
                                    <th class="py-3 px-4 text-left font-semibold">Ürün Sayısı</th>
                                    <th class="py-3 px-4 text-left font-semibold">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody id="categories-table">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Category Modal -->
    <div id="add-category-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50" aria-hidden="true" role="dialog" aria-labelledby="add-category-title">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="add-category-title" class="text-xl font-semibold">Yeni Kategori Ekle</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700" aria-label="Kapat">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="add-category-form">
                    <div class="form-group">
                        <label for="category-name" class="form-label">Kategori Adı</label>
                        <input type="text" id="category-name" class="form-input" required aria-required="true">
                    </div>
                    <div class="form-group">
                        <label for="category-description" class="form-label">Açıklama</label>
                        <textarea id="category-description" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal" aria-label="İptal">İptal</button>
                        <button type="submit" class="btn btn-primary" aria-label="Kaydet">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-confirmation-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <h3 class="text-xl font-semibold mb-4">Silme Onayı</h3>
                <p class="mb-4">Bu <span class="delete-confirmation-type">kategoriyi</span> silmek istediğinizden emin misiniz?</p>
                <input type="hidden" class="delete-confirmation-id">
                <div class="flex justify-end gap-2">
                    <button class="btn btn-secondary close-modal">İptal</button>
                    <button class="btn btn-danger" onclick="handleDeleteConfirmation(event)">Sil</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="edit-category-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Kategori Düzenle</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="edit-category-form">
                    <input type="hidden" id="edit-category-id">
                    <div class="form-group">
                        <label for="edit-category-name" class="form-label">Kategori Adı</label>
                        <input type="text" id="edit-category-name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-category-description" class="form-label">Açıklama</label>
                        <textarea id="edit-category-description" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal">İptal</button>
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Chart.js for reports    -->
   <script src="https://cdn.jsdelivr.net/npm/chart.js"> </script> 
 
    <!-- Main JavaScript -->
    <script src="app.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>