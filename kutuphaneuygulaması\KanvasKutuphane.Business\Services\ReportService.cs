using System;
using System.Threading.Tasks;
using KanvasKutuphane.Business.Services; // IReportService için
using KanvasKutuphane.Business.ViewModels; // ViewModels için
using KanvasKutuphane.DataAccess; // IUnitOfWork için
using KanvasKutuphane.Entities.ViewModels;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore; // Include için

namespace KanvasKutuphane.Business
{
    public class ReportService : IReportService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ReportService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<Business.ViewModels.PopularBookViewModel>> GetMostBorrowedBooksAsync(int count)
        {
            try
            {
                // Basit bir implementasyon - gerçek veriler yerine örnek veri
                var borrowings = await _unitOfWork.BorrowingRepository.GetAllAsync();
                var bookBorrowCounts = borrowings
                    .GroupBy(b => b.BookId)
                    .Select(g => new { BookId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .ToList();

                var result = new List<Business.ViewModels.PopularBookViewModel>();
                foreach (var item in bookBorrowCounts)
                {
                    var book = await _unitOfWork.BookRepository.GetByIdAsync(item.BookId);
                    if (book != null)
                    {
                        result.Add(new Business.ViewModels.PopularBookViewModel
                        {
                            BookTitle = book.Title,
                            BorrowCount = item.Count
                        });
                    }
                }
                return result;
            }
            catch
            {
                // Hata durumunda örnek veri döndür
                return new List<Business.ViewModels.PopularBookViewModel>
                {
                    new Business.ViewModels.PopularBookViewModel { BookTitle = "Örnek Kitap 1", BorrowCount = 15 },
                    new Business.ViewModels.PopularBookViewModel { BookTitle = "Örnek Kitap 2", BorrowCount = 12 },
                    new Business.ViewModels.PopularBookViewModel { BookTitle = "Örnek Kitap 3", BorrowCount = 8 }
                };
            }
        }

        public async Task<List<Business.ViewModels.LateReturnViewModel>> GetLateReturnsAsync()
        {
            try
            {
                var overdueBorrowings = await _unitOfWork.BorrowingRepository.GetAllAsync();
                var lateReturns = overdueBorrowings
                    .Where(b => b.DueDate.HasValue && b.DueDate.Value < DateTime.Now && !b.ReturnDate.HasValue)
                    .ToList();

                var result = new List<Business.ViewModels.LateReturnViewModel>();
                foreach (var borrowing in lateReturns)
                {
                    var book = await _unitOfWork.BookRepository.GetByIdAsync(borrowing.BookId);
                    var member = await _unitOfWork.MemberRepository.GetByIdAsync(borrowing.MemberId);

                    if (book != null && member != null)
                    {
                        var daysLate = (DateTime.Now - borrowing.DueDate.Value).Days;
                        result.Add(new Business.ViewModels.LateReturnViewModel
                        {
                            BookTitle = book.Title,
                            MemberName = member.FirstName + " " + member.LastName,
                            DueDate = borrowing.DueDate.Value,
                            DaysLate = daysLate
                        });
                    }
                }
                return result;
            }
            catch
            {
                // Hata durumunda örnek veri döndür
                return new List<Business.ViewModels.LateReturnViewModel>
                {
                    new Business.ViewModels.LateReturnViewModel
                    {
                        BookTitle = "Örnek Geciken Kitap",
                        MemberName = "Örnek Üye",
                        DueDate = DateTime.Now.AddDays(-5),
                        DaysLate = 5
                    }
                };
            }
        }

        public async Task<List<MemberBorrowingHistoryViewModel>> GetBorrowingHistoryByMemberIdAsync(int memberId)
        {
            try
            {
                var memberBorrowings = await _unitOfWork.BorrowingRepository.GetAllAsync();
                var borrowingHistory = memberBorrowings
                    .Where(b => b.MemberId == memberId)
                    .OrderByDescending(b => b.BorrowDate)
                    .ToList();

                var result = new List<Entities.ViewModels.MemberBorrowingHistoryViewModel>();
                foreach (var borrowing in borrowingHistory)
                {
                    var book = await _unitOfWork.BookRepository.GetByIdAsync(borrowing.BookId);
                    if (book != null)
                    {
                        result.Add(new Entities.ViewModels.MemberBorrowingHistoryViewModel
                        {
                            BookTitle = book.Title,
                            BorrowDate = borrowing.BorrowDate,
                            ReturnDate = borrowing.ReturnDate
                        });
                    }
                }
                return result;
            }
            catch
            {
                // Hata durumunda örnek veri döndür
                return new List<Entities.ViewModels.MemberBorrowingHistoryViewModel>
                {
                    new Entities.ViewModels.MemberBorrowingHistoryViewModel
                    {
                        BookTitle = "Örnek Kitap",
                        BorrowDate = DateTime.Now.AddDays(-10),
                        ReturnDate = DateTime.Now.AddDays(-3)
                    }
                };
            }
        }
    }
}
