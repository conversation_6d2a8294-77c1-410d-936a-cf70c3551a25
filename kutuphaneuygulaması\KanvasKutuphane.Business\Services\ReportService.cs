using System.Threading.Tasks;
using KanvasKutuphane.Business.Services; // IReportService için
using KanvasKutuphane.DataAccess; // IUnitOfWork için
using KanvasKutuphane.Entities.ViewModels;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore; // Include için

namespace KanvasKutuphane.Business
{
    public class ReportService : IReportService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ReportService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<List<PopularBookViewModel>> GetMostBorrowedBooksAsync(int count)
        {
            return (await _unitOfWork.BorrowingRepository.GetTopBorrowedBooksAsync(count))
                   .Select(b => new PopularBookViewModel
                   {
                       Title = b.Book.Title, // BookTitle yerine Title kullanıldı
                       BorrowCount = b.Id // BorrowingRepository'de Id'ye atanan <PERSON>
                   })
                   .ToList();
        }

        public async Task<List<LateReturnViewModel>> GetLateReturnsAsync()
        {
            return (await _unitOfWork.BorrowingRepository.GetOverdueBorrowingsAsync())
                   .Select(b => new LateReturnViewModel
                   {
                       BookTitle = b.Book.Title,
                       MemberName = b.Member.FirstName + " " + b.Member.LastName,
                       DueDate = b.DueDate.GetValueOrDefault()
                   })
                   .ToList();
        }

        public async Task<List<MemberBorrowingHistoryViewModel>> GetBorrowingHistoryByMemberIdAsync(int memberId)
        {
            return (await _unitOfWork.BorrowingRepository.GetBorrowingsByMemberIdAsync(memberId))
                   .Select(b => new MemberBorrowingHistoryViewModel
                   {
                       BookTitle = b.Book.Title,
                       BorrowDate = b.BorrowDate, // LoanDate yerine BorrowDate kullanıldı
                       ReturnDate = b.ReturnDate
                   })
                   .ToList();
        }
    }
}
