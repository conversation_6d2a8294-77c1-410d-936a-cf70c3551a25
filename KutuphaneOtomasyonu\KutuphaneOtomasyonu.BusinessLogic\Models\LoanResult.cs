using KutuphaneOtomasyonu.Entities;

namespace KutuphaneOtomasyonu.BusinessLogic.Models
{
    public class LoanResult
    {
        public bool Success { get; private set; }
        public string Message { get; private set; }
        public Loan Loan { get; private set; }

        private LoanResult(bool success, string message, Loan loan = null)
        {
            Success = success;
            Message = message;
            Loan = loan;
        }

        public static LoanResult Successful(string message, Loan loan = null)
        {
            return new LoanResult(true, message, loan);
        }

        public static LoanResult Failed(string message)
        {
            return new LoanResult(false, message);
        }
    }
}