using KanvasKutuphane.Entities; // AppUser için
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business.Services
{
    public interface IUserService
    {
        Task<IEnumerable<AppUser>> GetAllUsersAsync();
        Task<AppUser> GetUserByIdAsync(string id);
        Task<AppUser> GetUserByEmailAsync(string email);
        Task UpdateUserAsync(AppUser user);
        Task DeleteUserAsync(string id);
    }
}
