using KanvasKutuphane.DataAccess;
using KanvasKutuphane.Entities; // AppUser için - Changed from KanvasKutuphane.Entities.Models
using KanvasKutuphane.Business; // Added
using KanvasKutuphane.Business.Services; // Added
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder; // Added
using Microsoft.AspNetCore.Hosting; // Added for IsDevelopment
using Microsoft.Extensions.Configuration; // Added for GetConnectionString
using Microsoft.Extensions.Hosting; // Added for IsDevelopment

var builder = WebApplication.CreateBuilder(args);

// Veritabanı bağlantısı
builder.Services.AddDbContext<KanvasKutuphaneContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

// Identity servisi
builder.Services.AddIdentity<AppUser, IdentityRole>()
    .AddEntityFrameworkStores<KanvasKutuphaneContext>()
    .AddDefaultTokenProviders();

builder.Services.AddScoped<IBookService, BookManager>();
builder.Services.AddScoped<IBorrowingService, BorrowingManager>();
builder.Services.AddScoped<IMemberService, MemberManager>();
builder.Services.AddScoped<IReportService, ReportService>(); // Added ReportService
builder.Services.AddScoped<IUserService, UserService>(); // Added UserService
builder.Services.AddScoped<IExcelExportService, ExcelExportService>(); // Added Excel Export Service

builder.Services.AddScoped<IUnitOfWork, UnitOfWork>(); // UnitOfWork eklendi

builder.Services.AddScoped<IBookRepository, BookRepository>();
builder.Services.AddScoped<IBorrowingRepository, BorrowingRepository>();
builder.Services.AddScoped<IMemberRepository, MemberRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>(); // Added UserRepository

builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Login";
    options.AccessDeniedPath = "/Account/AccessDenied";
});

builder.Services.AddControllersWithViews();

// Authorization policies - Allow anonymous access to view pages, require admin for CRUD operations
builder.Services.AddAuthorization(options =>
{
    // No fallback policy - allow anonymous access by default
    // Individual controllers/actions will specify their own authorization requirements
});

var app = builder.Build();

app.Urls.Add("http://localhost:5000");


// SeedData kullanımı için app build edildikten sonra scope oluşturulmalı
using (var scope = app.Services.CreateScope())
{
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<AppUser>>();

    string[] roles = { "Admin", "Kütüphaneci", "Üye" };

    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
            await roleManager.CreateAsync(new IdentityRole(role));
    }

    // Varsayılan admin kullanıcı oluştur
    string adminEmail = "<EMAIL>";
    string adminPassword = "Admin123!";

    if (await userManager.FindByEmailAsync(adminEmail) == null)
    {
        var adminUser = new AppUser { UserName = adminEmail, Email = adminEmail };
        var result = await userManager.CreateAsync(adminUser, adminPassword);
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
        }
    }
}

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
}

app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
