using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.BusinessLogic.Services
{
    public interface IBookService
    {
        // Get
        Book GetBookById(int id);
        Task<Book> GetBookByIdAsync(int id);
        IEnumerable<Book> GetAllBooks();
        Task<IEnumerable<Book>> GetAllBooksAsync();
        IEnumerable<Book> SearchBooks(string searchTerm);
        Task<IEnumerable<Book>> SearchBooksAsync(string searchTerm);
        IEnumerable<Book> GetAllBooksWithCategories();
        Task<IEnumerable<Book>> GetAllBooksWithCategoriesAsync();

        // Add
        void AddBook(Book book);
        Task AddBookAsync(Book book);

        // Update
        void UpdateBook(Book book);
        Task UpdateBookAsync(Book book);

        // Delete
        void DeleteBook(int id);
        Task DeleteBookAsync(int id);

        // Stock
        bool IsBookAvailable(int id);
        Task<bool> IsBookAvailableAsync(int id);
        void UpdateBookStock(int id, int quantity);
        Task UpdateBookStockAsync(int id, int quantity);
    }
}
