using KanvasKutuphane.Business.Concrete;
using KanvasKutuphane.DataAccess.Repositories;
using KanvasKutuphane.Entities.Models;
using Moq;
using System.Collections.Generic;
using Xunit;

namespace KanvasKutuphane.Tests.Services
{
    public class MemberManagerTests
    {
        [Fact]
        public void Add_ShouldAddMember()
        {
            var mockRepo = new Mock<IMemberRepository>();
            var manager = new MemberManager(mockRepo.Object);

            var newMember = new Member { FirstName = "Ali", LastName = "Demir" };

            var exception = Record.Exception(() => manager.Add(newMember));

            Assert.Null(exception); // beklenen: hata olmamalı
        }
    }
}
