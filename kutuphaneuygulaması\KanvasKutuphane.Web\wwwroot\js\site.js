﻿// Kanvas <PERSON>üphane - Site JavaScript

document.addEventListener("DOMContentLoaded", function () {
    console.log("Kanvas <PERSON>ütüphane sistemi yüklendi!");

    // Initialize all interactive features
    initializeAnimations();
    initializeTooltips();
    initializeConfirmDialogs();
    initializeSearchFeatures();
    initializeThemeToggle();

    // Show loading complete message
    setTimeout(() => {
        console.log("Tüm özellikler aktif!");
    }, 1000);
});

// Animation initialization
function initializeAnimations() {
    // Add loading animation to all cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.classList.add('loading');
        setTimeout(() => {
            card.classList.add('loaded');
        }, index * 100);
    });

    // Add hover effects to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize confirmation dialogs for delete operations
function initializeConfirmDialogs() {
    const deleteButtons = document.querySelectorAll('[data-action="delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const itemName = this.getAttribute('data-item-name') || 'bu öğeyi';

            if (confirm(`${itemName} silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`)) {
                // If confirmed, proceed with the action
                const href = this.getAttribute('href');
                if (href) {
                    window.location.href = href;
                }
            }
        });
    });
}

// Initialize search features
function initializeSearchFeatures() {
    const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const targetTable = this.getAttribute('data-target') || 'table';
            const table = document.querySelector(targetTable);

            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                        row.classList.add('fade-in');
                    } else {
                        row.style.display = 'none';
                        row.classList.remove('fade-in');
                    }
                });
            }
        });
    });
}

// Initialize theme toggle (if needed)
function initializeThemeToggle() {
    const themeToggle = document.querySelector('#theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }
}

// Utility function to show notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat('tr-TR').format(num);
}

// Utility function to format dates
function formatDate(date) {
    return new Intl.DateTimeFormat('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

// Add smooth scrolling to anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading state to forms
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function() {
        const submitButton = this.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>İşleniyor...';
        }
    });
});

// Add auto-save functionality for forms (if needed)
function enableAutoSave(formSelector, saveInterval = 30000) {
    const form = document.querySelector(formSelector);
    if (form) {
        setInterval(() => {
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            localStorage.setItem(`autosave_${form.id}`, JSON.stringify(data));
            console.log('Form otomatik kaydedildi');
        }, saveInterval);
    }
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('JavaScript hatası:', e.error);
    showNotification('Bir hata oluştu. Lütfen sayfayı yenileyin.', 'danger');
});

// Export functions for use in other scripts
window.KanvasLibrary = {
    showNotification,
    formatNumber,
    formatDate,
    enableAutoSave
};
