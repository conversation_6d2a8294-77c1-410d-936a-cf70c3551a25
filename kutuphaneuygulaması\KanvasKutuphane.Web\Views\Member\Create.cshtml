@model KanvasKutuphane.Entities.Models.Member

@{
    ViewData["Title"] = "<PERSON><PERSON>";
}

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON></h2>

<form asp-action="Create" method="post" class="max-w-lg">
    <div class="mb-4">
        <label asp-for="FirstName" class="block font-semibold">Ad</label>
        <input asp-for="FirstName" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="FirstName" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="LastName" class="block font-semibold">Soyad</label>
        <input asp-for="LastName" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="LastName" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Email" class="block font-semibold">E-Posta</label>
        <input asp-for="Email" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Email" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Phone" class="block font-semibold">Telefon</label>
        <input asp-for="Phone" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Phone" class="text-red-500 text-sm"></span>
    </div>
    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Kaydet</button>
</form>
