@model KanvasKutuphane.Entities.Models.Member

@{
    ViewData["Title"] = "Ye<PERSON>";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-user-plus me-2 text-success"></i>Yeni <PERSON>
                </h1>
                <p class="text-muted mb-0">Kütüphaneye yeni bir <PERSON>ye e<PERSON>in</p>
            </div>
            <div>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Geri
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Form Section -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i><PERSON>ye Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" id="memberForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="FirstName" class="form-label fw-bold">
                                    <i class="fas fa-user me-1"></i>Ad
                                </label>
                                <input asp-for="FirstName" class="form-control" placeholder="Üyenin adını girin" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="LastName" class="form-label fw-bold">
                                    <i class="fas fa-user me-1"></i>Soyad
                                </label>
                                <input asp-for="LastName" class="form-control" placeholder="Üyenin soyadını girin" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label fw-bold">
                                    <i class="fas fa-envelope me-1"></i>E-posta Adresi
                                </label>
                                <input asp-for="Email" type="email" class="form-control" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>Geçerli bir e-posta adresi girin
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Phone" class="form-label fw-bold">
                                    <i class="fas fa-phone me-1"></i>Telefon Numarası
                                </label>
                                <input asp-for="Phone" type="tel" class="form-control" placeholder="0555 123 45 67" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>İletişim için telefon numarası
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a asp-action="Index" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>İptal
                                </a>
                                <button type="reset" class="btn btn-warning me-md-2">
                                    <i class="fas fa-undo me-1"></i>Temizle
                                </button>
                                <button type="submit" class="btn btn-save btn-pulse" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>Üyeyi Kaydet
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Help Section -->
<div class="row mt-4">
    <div class="col-lg-8 offset-lg-2">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>Üye Ekleme Yardımı
                </h6>
                <ul class="mb-0 small">
                    <li><strong>Ad ve Soyad:</strong> Üyenin tam adını girin</li>
                    <li><strong>E-posta:</strong> Geçerli bir e-posta adresi girin (iletişim için)</li>
                    <li><strong>Telefon:</strong> İletişim için telefon numarası girin</li>
                    <li><strong>Üye Numarası:</strong> Sistem tarafından otomatik olarak atanacaktır</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('memberForm');
            const submitBtn = document.getElementById('submitBtn');

            // Form submission handling
            form.addEventListener('submit', function(e) {
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.classList.add('btn-loading');

                // Reset button after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('btn-loading');
                }, 10000);
            });

            // Add input animations
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            // Phone number formatting
            const phoneInput = document.getElementById('Phone');
            if (phoneInput) {
                phoneInput.addEventListener('input', function() {
                    let value = this.value.replace(/\D/g, '');
                    if (value.length > 0) {
                        if (value.length <= 3) {
                            value = value;
                        } else if (value.length <= 6) {
                            value = value.slice(0, 3) + ' ' + value.slice(3);
                        } else if (value.length <= 8) {
                            value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6);
                        } else {
                            value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6, 8) + ' ' + value.slice(8, 10);
                        }
                    }
                    this.value = value;
                });
            }

            console.log('Üye ekleme formu yüklendi!');
        });
    </script>

    <style>
        .focused {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }

        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
}
