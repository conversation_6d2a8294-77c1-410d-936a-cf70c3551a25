using KutuphaneOtomasyonu.DataAccess.UnitOfWork;
using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.BusinessLogic.Services
{
    public class BookService : IBookService
    {
        private readonly IUnitOfWork _unitOfWork;

        public BookService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        // Get
        public Book GetBookById(int id)
        {
            return _unitOfWork.Books.GetById(id);
        }

        public async Task<Book> GetBookByIdAsync(int id)
        {
            return await _unitOfWork.Books.GetByIdAsync(id);
        }

        public IEnumerable<Book> GetAllBooks()
        {
            return _unitOfWork.Books.GetAll();
        }

        public async Task<IEnumerable<Book>> GetAllBooksAsync()
        {
            return await _unitOfWork.Books.GetAllAsync();
        }

        public IEnumerable<Book> SearchBooks(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return GetAllBooks();

            searchTerm = searchTerm.ToLower();
            return _unitOfWork.Books.Find(b =>
                b.Title.ToLower().Contains(searchTerm) ||
                b.Author.ToLower().Contains(searchTerm) ||
                b.ISBN.ToLower().Contains(searchTerm) ||
                b.Publisher.ToLower().Contains(searchTerm) ||
                b.Category.Name.ToLower().Contains(searchTerm)
            );
        }

        public async Task<IEnumerable<Book>> SearchBooksAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllBooksAsync();

            searchTerm = searchTerm.ToLower();
            return await _unitOfWork.Books.FindAsync(b =>
                b.Title.ToLower().Contains(searchTerm) ||
                b.Author.ToLower().Contains(searchTerm) ||
                b.ISBN.ToLower().Contains(searchTerm) ||
                b.Publisher.ToLower().Contains(searchTerm) ||
                b.Category.Name.ToLower().Contains(searchTerm)
            );
        }

        // Add
        public void AddBook(Book book)
        {
            if (book == null)
                throw new ArgumentNullException(nameof(book));

            if (_unitOfWork.Books.Exists(b => b.ISBN == book.ISBN))
                throw new InvalidOperationException($"Kitap zaten mevcut: ISBN {book.ISBN}");

            _unitOfWork.Books.Add(book);
            _unitOfWork.Complete();
        }

        public async Task AddBookAsync(Book book)
        {
            if (book == null)
                throw new ArgumentNullException(nameof(book));

            if (await _unitOfWork.Books.ExistsAsync(b => b.ISBN == book.ISBN))
                throw new InvalidOperationException($"Kitap zaten mevcut: ISBN {book.ISBN}");

            await _unitOfWork.Books.AddAsync(book);
            await _unitOfWork.CompleteAsync();
        }

        // Update
        public void UpdateBook(Book book)
        {
            if (book == null)
                throw new ArgumentNullException(nameof(book));

            var existingBook = _unitOfWork.Books.GetById(book.Id);
            if (existingBook == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {book.Id}");

            // ISBN değişmişse ve yeni ISBN başka bir kitapta kullanılıyorsa hata ver
            if (existingBook.ISBN != book.ISBN && _unitOfWork.Books.Exists(b => b.ISBN == book.ISBN))
                throw new InvalidOperationException($"Bu ISBN başka bir kitap tarafından kullanılıyor: {book.ISBN}");

            _unitOfWork.Books.Update(book);
            _unitOfWork.Complete();
        }

        public async Task UpdateBookAsync(Book book)
        {
            if (book == null)
                throw new ArgumentNullException(nameof(book));

            var existingBook = await _unitOfWork.Books.GetByIdAsync(book.Id);
            if (existingBook == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {book.Id}");

            // ISBN değişmişse ve yeni ISBN başka bir kitapta kullanılıyorsa hata ver
            if (existingBook.ISBN != book.ISBN && await _unitOfWork.Books.ExistsAsync(b => b.ISBN == book.ISBN))
                throw new InvalidOperationException($"Bu ISBN başka bir kitap tarafından kullanılıyor: {book.ISBN}");

            _unitOfWork.Books.Update(book);
            await _unitOfWork.CompleteAsync();
        }

        // Delete
        public void DeleteBook(int id)
        {
            var book = _unitOfWork.Books.GetById(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            // Kitap ödünç verilmişse silme
            if (_unitOfWork.Loans.Exists(l => l.BookId == id && l.ReturnDate == null))
                throw new InvalidOperationException($"Bu kitap şu anda ödünç verilmiş durumda, silinemez: ID {id}");

            _unitOfWork.Books.Remove(book);
            _unitOfWork.Complete();
        }

        public async Task DeleteBookAsync(int id)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            // Kitap ödünç verilmişse silme
            if (await _unitOfWork.Loans.ExistsAsync(l => l.BookId == id && l.ReturnDate == null))
                throw new InvalidOperationException($"Bu kitap şu anda ödünç verilmiş durumda, silinemez: ID {id}");

            _unitOfWork.Books.Remove(book);
            await _unitOfWork.CompleteAsync();
        }

        // Stock
        public bool IsBookAvailable(int id)
        {
            var book = _unitOfWork.Books.GetById(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            return book.Stock > 0;
        }

        public async Task<bool> IsBookAvailableAsync(int id)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            return book.Stock > 0;
        }

        public void UpdateBookStock(int id, int quantity)
        {
            var book = _unitOfWork.Books.GetById(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            if (book.Stock + quantity < 0)
                throw new InvalidOperationException($"Stok miktarı negatif olamaz: Mevcut {book.Stock}, Değişim {quantity}");

            book.Stock += quantity;
            _unitOfWork.Books.Update(book);
            _unitOfWork.Complete();
        }

        public async Task UpdateBookStockAsync(int id, int quantity)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(id);
            if (book == null)
                throw new InvalidOperationException($"Kitap bulunamadı: ID {id}");

            if (book.Stock + quantity < 0)
                throw new InvalidOperationException($"Stok miktarı negatif olamaz: Mevcut {book.Stock}, Değişim {quantity}");

            book.Stock += quantity;
            _unitOfWork.Books.Update(book);
            await _unitOfWork.CompleteAsync();
        }

        public IEnumerable<Book> GetAllWithCategory()
        {
            return _context.Books.Include(b => b.Category).ToList();
        }

        public IEnumerable<Book> GetAllBooksWithCategory()
        {
            return _unitOfWork.Books.GetAll().Select(b => new Book
            {
                Id = b.Id,
                Title = b.Title,
                Author = b.Author,
                ISBN = b.ISBN,
                Publisher = b.Publisher,
                Category = b.Category
            });
        }

        public async Task<IEnumerable<Book>> GetAllBooksWithCategoryAsync()
        {
            var books = await _unitOfWork.Books.GetAllAsync();
            return books.Select(b => new Book
            {
                Id = b.Id,
                Title = b.Title,
                Author = b.Author,
                ISBN = b.ISBN,
                Publisher = b.Publisher,
                Category = b.Category
            });
        }
    }
}
