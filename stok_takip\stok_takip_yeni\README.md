# Stok Takip Uygulaması

Modern ve kullanıcı dostu bir arayüze sahip web tabanlı stok takip uygulaması.

## Özellikler

- <PERSON><PERSON><PERSON><PERSON> ekle<PERSON>, düzenleme ve silme
- Kate<PERSON>i yönetimi
- Stok durumu takibi
- Düşük stok uyarıları
- Arama ve filtreleme
- Responsive tasarım

## Teknolojiler

- HTML5
- CSS3
- JavaScript (ES6+)
- Node.js
- SQLite

## Kurulum

1. Projeyi klonlayın:
```bash
git clone https://github.com/sirrakadembasti/stok_takip.git
```

2. Proje dizinine gidin:
```bash
cd stok_takip_yeni
```

3. Gerekli paketleri yükleyin:
```bash
npm install
```

4. Uygulamayı başlatın:
```bash
node server.js
```

5. Tarayıcınızda aşağıdaki adresi açın:
```
http://localhost:3000
```

## Kullanım

- Ana sayfada ürünlerin listesini görebilirsiniz
- "Yeni Ürün Ekle" butonu ile yeni ürün ekleyebilirsiniz
- Ürünleri kategoriye göre filtreleyebilirsiniz
- Stok durumuna göre filtreleme yapabilirsiniz
- Arama kutusu ile ürün adı veya kodu ile arama yapabilirsiniz

## Lisans

MIT

## İletişim

GitHub: [@sirrakadembasti](https://github.com/sirrakadembasti)