@model IEnumerable<KutuphaneOtomasyonu.Entities.Category>
@{
    ViewData["Title"] = "Kategoriler";
}

<div class="container">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Kategoriler</h4>
            <a asp-action="CreateCategory" class="btn btn-light">Ye<PERSON></a>
        </div>
        <div class="card-body">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success">
                    @TempData["SuccessMessage"]
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger">
                    @TempData["ErrorMessage"]
                </div>
            }

            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var category in Model)
                        {
                            <tr>
                                <td>@category.Name</td>
                                <td>
                                    <div class="btn-group">
                                        <a asp-action="EditCategory" asp-route-id="@category.Id" class="btn btn-sm btn-primary">Düzenle</a>
                                        <a asp-action="DeleteCategory" asp-route-id="@category.Id" class="btn btn-sm btn-danger">Sil</a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>