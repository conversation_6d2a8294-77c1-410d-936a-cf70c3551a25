using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using KanvasKutuphane.DataAccess;
using OfficeOpenXml;

namespace KanvasKutuphane.Business.Services
{
    public class ExcelExportService : IExcelExportService
    {
        private readonly IUnitOfWork _unitOfWork;

        public ExcelExportService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<byte[]> ExportMembersToExcelAsync()
        {
            var membersEnumerable = await _unitOfWork.MemberRepository.GetAllAsync();
            var members = membersEnumerable.ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Üye Listesi");

            // Headers
            worksheet.Cells[1, 1].Value = "Üye No";
            worksheet.Cells[1, 2].Value = "Ad";
            worksheet.Cells[1, 3].Value = "Soyad";
            worksheet.Cells[1, 4].Value = "E-posta";
            worksheet.Cells[1, 5].Value = "Telefon";
            worksheet.Cells[1, 6].Value = "Kayıt Tarihi";
            worksheet.Cells[1, 7].Value = "Durum";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
            }

            // Data
            for (int i = 0; i < members.Count; i++)
            {
                var member = members[i];
                int row = i + 2;

                worksheet.Cells[row, 1].Value = member.MemberNumber;
                worksheet.Cells[row, 2].Value = member.FirstName;
                worksheet.Cells[row, 3].Value = member.LastName;
                worksheet.Cells[row, 4].Value = member.Email;
                worksheet.Cells[row, 5].Value = member.Phone;
                worksheet.Cells[row, 6].Value = DateTime.Now.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 7].Value = "Aktif";
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> ExportBooksToExcelAsync()
        {
            var booksEnumerable = await _unitOfWork.BookRepository.GetAllAsync();
            var books = booksEnumerable.ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Kitap Envanteri");

            // Headers
            worksheet.Cells[1, 1].Value = "Kitap ID";
            worksheet.Cells[1, 2].Value = "Başlık";
            worksheet.Cells[1, 3].Value = "Yazar";
            worksheet.Cells[1, 4].Value = "Tür";
            worksheet.Cells[1, 5].Value = "Yayın Yılı";
            worksheet.Cells[1, 6].Value = "Stok Miktarı";
            worksheet.Cells[1, 7].Value = "Durum";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
            }

            // Data
            for (int i = 0; i < books.Count; i++)
            {
                var book = books[i];
                int row = i + 2;

                worksheet.Cells[row, 1].Value = book.Id;
                worksheet.Cells[row, 2].Value = book.Title;
                worksheet.Cells[row, 3].Value = book.Author;
                worksheet.Cells[row, 4].Value = book.Genre ?? "Belirtilmemiş";
                worksheet.Cells[row, 5].Value = book.PublicationYear;
                worksheet.Cells[row, 6].Value = book.StockQuantity;
                worksheet.Cells[row, 7].Value = book.StockQuantity > 0 ? "Mevcut" : "Stokta Yok";
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> ExportBorrowedBooksToExcelAsync()
        {
            var borrowingsEnumerable = await _unitOfWork.BorrowingRepository.GetAllAsync();
            var activeBorrowings = borrowingsEnumerable.Where(b => !b.ReturnDate.HasValue).ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Ödünç Verilen Kitaplar");

            // Headers
            worksheet.Cells[1, 1].Value = "Ödünç ID";
            worksheet.Cells[1, 2].Value = "Kitap Başlığı";
            worksheet.Cells[1, 3].Value = "Üye Adı";
            worksheet.Cells[1, 4].Value = "Ödünç Tarihi";
            worksheet.Cells[1, 5].Value = "Teslim Tarihi";
            worksheet.Cells[1, 6].Value = "Durum";
            worksheet.Cells[1, 7].Value = "Gecikme (Gün)";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
            }

            // Data
            for (int i = 0; i < activeBorrowings.Count; i++)
            {
                var borrowing = activeBorrowings[i];
                int row = i + 2;

                var book = await _unitOfWork.BookRepository.GetByIdAsync(borrowing.BookId);
                var member = await _unitOfWork.MemberRepository.GetByIdAsync(borrowing.MemberId);

                worksheet.Cells[row, 1].Value = borrowing.Id;
                worksheet.Cells[row, 2].Value = book?.Title ?? "Bilinmeyen Kitap";
                worksheet.Cells[row, 3].Value = member != null ? $"{member.FirstName} {member.LastName}" : "Bilinmeyen Üye";
                worksheet.Cells[row, 4].Value = borrowing.BorrowDate.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 5].Value = borrowing.DueDate?.ToString("dd.MM.yyyy") ?? "Belirtilmemiş";
                
                var isLate = borrowing.DueDate.HasValue && borrowing.DueDate.Value < DateTime.Now;
                worksheet.Cells[row, 6].Value = isLate ? "Gecikmiş" : "Normal";
                
                if (isLate && borrowing.DueDate.HasValue)
                {
                    var daysLate = (DateTime.Now - borrowing.DueDate.Value).Days;
                    worksheet.Cells[row, 7].Value = daysLate;
                }
                else
                {
                    worksheet.Cells[row, 7].Value = 0;
                }
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> ExportPopularBooksToExcelAsync(int count = 10)
        {
            var borrowingsEnumerable = await _unitOfWork.BorrowingRepository.GetAllAsync();
            var bookBorrowCounts = borrowingsEnumerable
                .GroupBy(b => b.BookId)
                .Select(g => new { BookId = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(count)
                .ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Popüler Kitaplar");

            // Headers
            worksheet.Cells[1, 1].Value = "Sıra";
            worksheet.Cells[1, 2].Value = "Kitap Başlığı";
            worksheet.Cells[1, 3].Value = "Yazar";
            worksheet.Cells[1, 4].Value = "Ödünç Alınma Sayısı";
            worksheet.Cells[1, 5].Value = "Popülerlik Seviyesi";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 5])
            {
                range.Style.Font.Bold = true;
            }

            // Data
            for (int i = 0; i < bookBorrowCounts.Count; i++)
            {
                var item = bookBorrowCounts[i];
                int row = i + 2;

                var book = await _unitOfWork.BookRepository.GetByIdAsync(item.BookId);

                worksheet.Cells[row, 1].Value = i + 1;
                worksheet.Cells[row, 2].Value = book?.Title ?? "Bilinmeyen Kitap";
                worksheet.Cells[row, 3].Value = book?.Author ?? "Bilinmeyen Yazar";
                worksheet.Cells[row, 4].Value = item.Count;
                
                var popularityLevel = item.Count switch
                {
                    >= 20 => "Çok Popüler",
                    >= 10 => "Popüler",
                    >= 5 => "Orta",
                    _ => "Az"
                };
                worksheet.Cells[row, 5].Value = popularityLevel;
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> ExportLateReturnsToExcelAsync()
        {
            var borrowingsEnumerable = await _unitOfWork.BorrowingRepository.GetAllAsync();
            var lateReturns = borrowingsEnumerable
                .Where(b => b.DueDate.HasValue && b.DueDate.Value < DateTime.Now && !b.ReturnDate.HasValue)
                .OrderBy(b => b.DueDate)
                .ToList();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Geciken İadeler");

            // Headers
            worksheet.Cells[1, 1].Value = "Ödünç ID";
            worksheet.Cells[1, 2].Value = "Kitap Başlığı";
            worksheet.Cells[1, 3].Value = "Üye Adı";
            worksheet.Cells[1, 4].Value = "Ödünç Tarihi";
            worksheet.Cells[1, 5].Value = "Teslim Tarihi";
            worksheet.Cells[1, 6].Value = "Gecikme (Gün)";
            worksheet.Cells[1, 7].Value = "Durum";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
            }

            // Data
            for (int i = 0; i < lateReturns.Count; i++)
            {
                var borrowing = lateReturns[i];
                int row = i + 2;

                var book = await _unitOfWork.BookRepository.GetByIdAsync(borrowing.BookId);
                var member = await _unitOfWork.MemberRepository.GetByIdAsync(borrowing.MemberId);
                var daysLate = (DateTime.Now - borrowing.DueDate!.Value).Days;

                worksheet.Cells[row, 1].Value = borrowing.Id;
                worksheet.Cells[row, 2].Value = book?.Title ?? "Bilinmeyen Kitap";
                worksheet.Cells[row, 3].Value = member != null ? $"{member.FirstName} {member.LastName}" : "Bilinmeyen Üye";
                worksheet.Cells[row, 4].Value = borrowing.BorrowDate.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 5].Value = borrowing.DueDate.Value.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 6].Value = daysLate;
                
                var status = daysLate switch
                {
                    <= 3 => "Az Gecikmiş",
                    <= 7 => "Gecikmiş",
                    _ => "Çok Gecikmiş"
                };
                worksheet.Cells[row, 7].Value = status;
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }
    }
}
