@model KanvasKutuphane.Entities.Models.Member

@{
    ViewData["Title"] = "Üye Sil";
}

<h2 class="text-2xl font-bold mb-4 text-red-600"><PERSON><PERSON> Sil</h2>

<div class="mb-4">
    <p><strong>Ad:</strong> @Model.FirstName</p>
    <p><strong>Soyad:</strong> @Model.LastName</p>
    <p><strong>E-Posta:</strong> @Model.Email</p>
    <p><strong>Telefon:</strong> @Model.Phone</p>
</div>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded">Sil</button>
    <a asp-action="Index" class="ml-4 text-blue-600 hover:underline">İptal</a>
</form>
