@using Microsoft.AspNetCore.Identity
@using KanvasKutuphane.Entities; // AppUser için - Changed from KanvasKutuphane.Entities.Models
@inject SignInManager<AppUser> SignInManager
@inject UserManager<AppUser> UserManager

@if (SignInManager.IsSignedIn(User))
{
    <div>
        <span class="mr-4"><PERSON><PERSON><PERSON><PERSON>, @UserManager.GetUserName(User)</span>
        <form method="post" asp-area="" asp-controller="Account" asp-action="Logout" class="inline">
            <button type="submit" class="bg-red-500 text-white px-4 py-1 rounded">Çık<PERSON>ş</button>
        </form>
    </div>
}
else
{
    <a asp-area="" asp-controller="Account" asp-action="Login" class="text-blue-600">G<PERSON><PERSON> Yap</a>
}
