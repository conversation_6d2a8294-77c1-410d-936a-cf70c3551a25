using KanvasKutuphane.Entities.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.DataAccess
{
    public interface IBorrowingRepository
    {
        Task<IEnumerable<Borrowing>> GetAllAsync();
        Task<Borrowing> GetByIdAsync(int id);
        Task AddAsync(Borrowing borrowing);
        Task UpdateAsync(Borrowing borrowing);
        Task DeleteAsync(int id);

        Task<int> CountAsync(System.Linq.Expressions.Expression<System.Func<Borrowing, bool>> predicate);
        Task<IEnumerable<Borrowing>> GetOverdueBorrowingsAsync();
        Task<int> GetBorrowedBookCountByMemberIdAsync(int memberId);
        Task<IEnumerable<Borrowing>> GetBorrowingsByMemberIdAsync(int memberId);
        Task<IEnumerable<Borrowing>> GetTopBorrowedBooksAsync(int topCount);
    }
}
