@model KanvasKutuphane.Entities.Models.Book

@{
    ViewData["Title"] = "Kitap Düzenle";
}

<h2 class="text-2xl font-bold mb-4">Kitap Düzenle</h2>

<form asp-action="Edit" method="post" class="max-w-lg">
    <input type="hidden" asp-for="Id" />
    <div class="mb-4">
        <label asp-for="Title" class="block font-semibold">Başlık</label>
        <input asp-for="Title" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Title" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Author" class="block font-semibold">Yazar</label>
        <input asp-for="Author" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Author" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Genre" class="block font-semibold">Tür</label>
        <input asp-for="Genre" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Genre" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Stock" class="block font-semibold">Stok</label>
        <input asp-for="Stock" type="number" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="Stock" class="text-red-500 text-sm"></span>
    </div>
    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Güncelle</button>
</form>
