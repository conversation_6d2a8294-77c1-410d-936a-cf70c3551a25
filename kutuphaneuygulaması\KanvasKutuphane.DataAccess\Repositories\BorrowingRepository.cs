
using KanvasKutuphane.Entities.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KanvasKutuphane.DataAccess
{
    public class BorrowingRepository : IBorrowingRepository
    {
       private readonly KanvasKutuphaneContext _context;


        public BorrowingRepository(KanvasKutuphaneContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Borrowing>> GetAllAsync()
        {
            return await _context.Borrowings
                .Include(b => b.Book)
                .Include(b => b.Member)
                .ToListAsync();
        }

        public async Task<Borrowing> GetByIdAsync(int id)
        {
            return await _context.Borrowings
                .Include(b => b.Book)
                .Include(b => b.Member)
                .FirstOrDefaultAsync(b => b.Id == id);
        }

        public async Task AddAsync(Borrowing borrowing)
        {
            await _context.Borrowings.AddAsync(borrowing);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Borrowing borrowing)
        {
            _context.Borrowings.Update(borrowing);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var borrowing = await _context.Borrowings.FindAsync(id);
            if (borrowing != null)
            {
                _context.Borrowings.Remove(borrowing);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> CountAsync(System.Linq.Expressions.Expression<System.Func<Borrowing, bool>> predicate)
        {
            return await _context.Borrowings.CountAsync(predicate);
        }

        public async Task<IEnumerable<Borrowing>> GetOverdueBorrowingsAsync()
        {
            return await _context.Borrowings
                .Include(b => b.Book)
                .Include(b => b.Member)
                .Where(b => b.ReturnDate == null && b.DueDate < DateTime.Now)
                .ToListAsync();
        }

        public async Task<int> GetBorrowedBookCountByMemberIdAsync(int memberId)
        {
            return await _context.Borrowings
                .CountAsync(b => b.MemberId == memberId && b.ReturnDate == null);
        }

        public async Task<IEnumerable<Borrowing>> GetBorrowingsByMemberIdAsync(int memberId)
        {
            return await _context.Borrowings
                .Include(b => b.Book)
                .Include(b => b.Member)
                .Where(b => b.MemberId == memberId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Borrowing>> GetTopBorrowedBooksAsync(int topCount)
        {
            return await _context.Borrowings
                .Include(b => b.Book)
                .GroupBy(b => b.BookId)
                .Select(g => new Borrowing
                {
                    Book = g.First().Book,
                    BookId = g.Key,
                    Id = g.Count()
                })
                .OrderByDescending(b => b.Id)
                .Take(topCount)
                .ToListAsync();
        }
    }
}
