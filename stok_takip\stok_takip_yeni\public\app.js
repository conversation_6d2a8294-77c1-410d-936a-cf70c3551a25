// Stok Takip Sistemi - Main JavaScript

// DOM Elements
const sections = document.querySelectorAll('main section');
const navLinks = document.querySelectorAll('nav a');
const mobileMenuButton = document.getElementById('mobile-menu-button');
const sidebarMenu = document.getElementById('sidebar-menu');

// Modals
const addProductModal = document.getElementById('add-product-modal');
const addCategoryModal = document.getElementById('add-category-modal');
const addStockModal = document.getElementById('add-stock-modal');
const removeStockModal = document.getElementById('remove-stock-modal');
const editProductModal = document.getElementById('edit-product-modal');
const deleteConfirmationModal = document.getElementById('delete-confirmation-modal');

// Buttons
const addProductBtn = document.getElementById('add-product-btn');
const addCategoryBtn = document.getElementById('add-category-btn');
const addStockBtn = document.getElementById('add-stock-btn');
const removeStockBtn = document.getElementById('remove-stock-btn');
const saveSettingsBtn = document.getElementById('save-settings');
const filterTransactionsBtn = document.getElementById('filter-transactions');

// Forms
const addProductForm = document.getElementById('add-product-form');
const addCategoryForm = document.getElementById('add-category-form');
const addStockForm = document.getElementById('add-stock-form');
const removeStockForm = document.getElementById('remove-stock-form');
const editProductForm = document.getElementById('edit-product-form');

// Tables
const productsTable = document.getElementById('products-table');
const categoriesTable = document.getElementById('categories-table');
const transactionsTable = document.getElementById('transactions-table');
const recentActivitiesTable = document.getElementById('recent-activities');
const lowStockTable = document.getElementById('low-stock-table');

// Stats Elements
const totalProductsElement = document.getElementById('total-products');
const inStockElement = document.getElementById('in-stock');
const lowStockElement = document.getElementById('low-stock');
const totalCategoriesElement = document.getElementById('total-categories');

// Charts
let stockValueChart;
let categoryDistributionChart;
let stockMovementChart;

// Define missing chart variables
let productListChart;
let stockInListChart;
let stockOutListChart;

// App State
let products = [];
let categories = [];
let transactions = [];
let settings = {
    companyName: 'Şirketim',
    lowStockThreshold: 5,
    currency: 'TRY',
    dateFormat: 'DD/MM/YYYY'
};

// Currency symbols
const currencySymbols = {
    'TRY': '₺',
    'USD': '$',
    'EUR': '€',
    'GBP': '£'
};

// Local storage keys
const STORAGE_KEYS = {
    PRODUCTS: 'stok_takip_products',
    CATEGORIES: 'stok_takip_categories',
    TRANSACTIONS: 'stok_takip_transactions'
};

// Initialize local storage with sample data if empty
function initializeLocalStorage() {
    if (!localStorage.getItem(STORAGE_KEYS.PRODUCTS)) {
        localStorage.setItem(STORAGE_KEYS.PRODUCTS, JSON.stringify([]));
    }
    if (!localStorage.getItem(STORAGE_KEYS.CATEGORIES)) {
        localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify([]));
    }
    if (!localStorage.getItem(STORAGE_KEYS.TRANSACTIONS)) {
        localStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify([]));
    }
}

// Database operations
const db = {
    getProducts: () => JSON.parse(localStorage.getItem(STORAGE_KEYS.PRODUCTS) || '[]'),
    saveProducts: (products) => localStorage.setItem(STORAGE_KEYS.PRODUCTS, JSON.stringify(products)),
    getCategories: () => JSON.parse(localStorage.getItem(STORAGE_KEYS.CATEGORIES) || '[]'),
    saveCategories: (categories) => localStorage.setItem(STORAGE_KEYS.CATEGORIES, JSON.stringify(categories)),
    getTransactions: () => JSON.parse(localStorage.getItem(STORAGE_KEYS.TRANSACTIONS) || '[]'),
    saveTransactions: (transactions) => localStorage.setItem(STORAGE_KEYS.TRANSACTIONS, JSON.stringify(transactions))
};

// API URL'i göreceli yol olarak güncellendi
const apiUrl = '';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    loadData();
    initializeUI();
    setupEventListeners();
    setupNavigation();
    
    // Highlight current page in navigation
    const currentPage = window.location.pathname.split('/').pop();
    document.querySelectorAll('.nav-link').forEach(link => {
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('bg-indigo-600');
        }
    });
});

// Yeni loadData bölümü
function loadData() {
    Promise.all([
        fetch(`${apiUrl}/api/products`).then(res => res.json()).then(data => { products = data; }),
        fetch(`${apiUrl}/api/categories`).then(res => res.json()).then(data => { categories = data; }),
        fetch(`${apiUrl}/api/transactions`).then(res => res.json()).then(data => { transactions = data; })
    ]).then(() => {
        const currentPage = window.location.pathname.split('/').pop();
        if (currentPage === 'dashboard.html') updateDashboard();
        if (currentPage === 'products.html') renderProductsTable();
        if (currentPage === 'categories.html') renderCategoriesTable();
        if (currentPage === 'transactions.html') renderTransactionsTable();
        if (currentPage === 'reports.html') initializeCharts();
    });
}

// Save data to localStorage
function saveData() {
    // Removed: Data persistence is now handled on the server side (SQLlite).
}

// Initialize UI elements
function initializeUI() {
    // Populate settings form if the element exists
    const companyNameField = document.getElementById('company-name');
    if (companyNameField) {
        companyNameField.value = settings.companyName;
    }
    const lowStockField = document.getElementById('low-stock-threshold');
    if (lowStockField) {
        lowStockField.value = settings.lowStockThreshold;
    }
    const currencyField = document.getElementById('currency');
    if (currencyField) {
        currencyField.value = settings.currency;
    }
    const dateFormatField = document.getElementById('date-format');
    if (dateFormatField) {
        dateFormatField.value = settings.dateFormat;
    }
    
    // Set today's date for stock forms if available
    const today = new Date().toISOString().split('T')[0];
    const stockDateField = document.getElementById('stock-date');
    if (stockDateField) {
        stockDateField.value = today;
    }
    const removeStockDateField = document.getElementById('remove-stock-date');
    if (removeStockDateField) {
        removeStockDateField.value = today;
    }
    
    // Populate dropdowns
    updateCategoryDropdowns();
    updateProductDropdowns();
}

// Update setupEventListeners function
function setupEventListeners() {
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-button');
    const sidebarMenu = document.getElementById('sidebar-menu');
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', () => {
            sidebarMenu.classList.toggle('hidden');
        });
    }

    // Button Event Listeners based on current page
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    switch(currentPage) {
        case 'products':
            setupProductPageEvents();
            break;
        case 'categories':
            setupCategoryPageEvents();
            break;
        case 'transactions':
            setupTransactionPageEvents();
            break;
        case 'settings':
            setupSettingsPageEvents();
            break;
        case 'reports':
            setupReportsPageEvents();
            break;
    }

    // Common search and filter functionality
    setupSearchAndFilters();
}

function setupProductPageEvents() {
    const addProductBtn = document.getElementById('add-product-btn');
    const addProductModal = document.getElementById('add-product-modal');
    
    if (addProductBtn) {
        addProductBtn.addEventListener('click', () => {
            openModal(addProductModal);
            updateCategoryDropdowns(); // Populate category dropdown
        });
    }

    // Close modal buttons
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });

    // Form submit handler is already defined in handleAddProduct function
}

function setupCategoryPageEvents() {
    const addCategoryBtn = document.getElementById('add-category-btn');
    const addCategoryModal = document.getElementById('add-category-modal');
    const addCategoryForm = document.getElementById('add-category-form');
    
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', () => {
            openModal(addCategoryModal);
        });
    }

    if (addCategoryForm) {
        addCategoryForm.addEventListener('submit', handleAddCategory);
    }
}

function handleAddCategory(e) {
    e.preventDefault();
    
    const nameInput = document.getElementById('category-name');
    const name = nameInput ? nameInput.value.trim() : '';
    const description = document.getElementById('category-description').value;

    if (!name) {
        showNotification('Kategori adı boş olamaz', 'error');
        return;
    }

    const newCategory = {
        id: generateCategoryId(),
        name,
        description
    };

    fetch(`${apiUrl}/api/categories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCategory)
    })
    .then(res => res.json())
    .then(data => {
        categories.push(data);
        showNotification('Kategori başarıyla eklendi', 'success');
        renderCategoriesTable();
        closeModal(document.getElementById('add-category-modal'));
        document.getElementById('add-category-form').reset();
    })
    .catch(err => {
        console.error('Kategori ekleme hatası:', err);
        showNotification('Kategori eklenirken hata oluştu', 'error');
    });
}

function setupTransactionPageEvents() {
    const addStockBtn = document.getElementById('add-stock-btn');
    const removeStockBtn = document.getElementById('remove-stock-btn');
    const addStockModal = document.getElementById('add-stock-modal');
    const removeStockModal = document.getElementById('remove-stock-modal');

    if (addStockBtn) {
        addStockBtn.addEventListener('click', () => {
            openModal(addStockModal);
            updateProductDropdowns();
        });
    }

    if (removeStockBtn) {
        removeStockBtn.addEventListener('click', () => {
            openModal(removeStockModal);
            updateProductDropdowns();
        });
    }

    // Close modal buttons
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });

    // Initialize datepickers with today's date
    const today = new Date().toISOString().split('T')[0];
    const dateInputs = ['stock-date', 'remove-stock-date'];
    dateInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) input.value = today;
    });
}

function setupSettingsPageEvents() {
    const saveSettingsBtn = document.getElementById('save-settings');
    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', (e) => {
            e.preventDefault();
            saveSettings();
        });
    }
}

function setupReportsPageEvents() {
    const exportExcelBtn = document.getElementById('export-excel');
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', exportToExcel);
    }
    // Yeni: Hareketler için ayrı bir buton varsa onu da bağla
    const exportTransactionsBtn = document.getElementById('export-transactions-excel');
    if (exportTransactionsBtn) {
        exportTransactionsBtn.addEventListener('click', exportTransactionsToExcel);
    }
}

function setupSearchAndFilters() {
    // Product search
    const searchProduct = document.getElementById('search-product');
    if (searchProduct) {
        searchProduct.addEventListener('input', (e) => {
            filterProducts(e.target.value.toLowerCase());
        });
    }

    // Transaction search
    const searchTransaction = document.getElementById('search-transaction');
    if (searchTransaction) {
        searchTransaction.addEventListener('input', (e) => {
            filterTransactions(e.target.value.toLowerCase());
        });
    }

    // Filter dropdowns
    const filterSelects = document.querySelectorAll('select[id*="filter"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', () => {
            const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
            if (currentPage === 'products') {
                filterProducts();
            } else if (currentPage === 'transactions') {
                filterTransactions();
            }
        });
    });
}

// Add these new handler functions
function handleSearch(searchTerm) {
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    switch(currentPage) {
        case 'products':
            filterProducts(searchTerm);
            break;
        case 'categories':
            filterCategories();
            break;
        case 'transactions':
            filterTransactions(searchTerm);
            break;
    }
}

function handleFilter() {
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    switch(currentPage) {
        case 'products':
            filterProducts();
            break;
        case 'transactions':
            filterTransactions();
            break;
    }
}

// Page loading function


// Show a specific section and hide others

function showSection(sectionId) {
    sections.forEach(section => {
        section.classList.add('hidden');
    });
    
    const currentSection = document.getElementById(sectionId);
    if (currentSection) {
        currentSection.classList.remove('hidden');
        
        // Initialize charts if showing reports section
        if (sectionId === 'reports') {
            initializeCharts();
        }
        
        // Refresh tables when showing their sections
        if (sectionId === 'products') {
            renderProductsTable();
        } else if (sectionId === 'categories') {
            renderCategoriesTable();
        } else if (sectionId === 'transactions') {
            renderTransactionsTable();
        }
    }
}

// Open a modal
function openModal(modal) {
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// Close a modal
function closeModal(modal) {
    if (modal) {
        modal.classList.add('hidden');
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// Update dashboard stats and tables
function updateDashboard() {
    // Güvenli element güncellemesi
    if (totalProductsElement) {
        totalProductsElement.textContent = products.length;
    }
    
    if (inStockElement) {
        const inStockCount = products.filter(product => product.stock > 0).length;
        inStockElement.textContent = inStockCount;
    }
    
    if (lowStockElement) {
        const lowStockCount = products.filter(product => product.stock > 0 && product.stock <= settings.lowStockThreshold).length;
        lowStockElement.textContent = lowStockCount;
    }
    
    if (totalCategoriesElement) {
        totalCategoriesElement.textContent = categories.length;
    }
    
    // Sadece gerekli elementler varsa tabloları güncelle
    if (recentActivitiesTable) {
        renderRecentActivities();
    }
    
    if (lowStockTable) {
        renderLowStockProducts();
    }
}

// Render recent activities table
function renderRecentActivities() {
    recentActivitiesTable.innerHTML = '';
    // Get the 5 most recent transactions
    const recentTransactions = [...transactions]
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 5);
    
    if (recentTransactions.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">Henüz işlem bulunmuyor</td>
        `;
        recentActivitiesTable.appendChild(emptyRow);
        return;
    }
    
    recentTransactions.forEach(transaction => {
        const product = products.find(p => p.id === transaction.productId);
        const productName = product ? product.name : 'Silinmiş Ürün';
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-2 px-4">${formatDate(transaction.date)}</td>
            <td class="py-2 px-4">${productName}</td>
            <td class="py-2 px-4">
                <span class="${transaction.type === 'in' ? 'text-green-600' : 'text-red-600'}">
                    ${transaction.type === 'in' ? 'Giriş' : 'Çıkış'}
                </span>
            </td>
            <td class="py-2 px-4">${transaction.quantity} ${product ? product.unit : ''}</td>
        `;
        recentActivitiesTable.appendChild(row);
    });
}

// Render low stock products table
function renderLowStockProducts() {
    lowStockTable.innerHTML = '';
    // Get products with low stock
    const lowStockProducts = products.filter(product => 
        product.stock > 0 && product.stock <= settings.lowStockThreshold
    );
    
    if (lowStockProducts.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">Kritik stok ürün bulunmuyor</td>
        `;
        lowStockTable.appendChild(emptyRow);
        return;
    }
    
    lowStockProducts.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-2 px-4">${product.code}</td>
            <td class="py-2 px-4">${product.name}</td>
            <td class="py-2 px-4">${product.stock} ${product.unit}</td>
            <td class="py-2 px-4">
                <span class="status-badge status-low-stock">Kritik Stok</span>
            </td>
        `;
        lowStockTable.appendChild(row);
    });
}

// Render products table
function renderProductsTable() {
    productsTable.innerHTML = '';
    
    if (products.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">Henüz ürün bulunmuyor</td>
        `;
        productsTable.appendChild(emptyRow);
        return;
    }
    
    // En büyük numaralı ürün en üstte
    const sortedProducts = [...products].sort((a, b) => {
        const anum = parseInt((a.code || '').replace('PRD', ''));
        const bnum = parseInt((b.code || '').replace('PRD', ''));
        return bnum - anum;
    });
    sortedProducts.forEach(product => {
        const category = categories.find(c => c.id === product.categoryId);
        const categoryName = category ? category.name : 'Kategori Yok';
        
        let stockStatus = '';
        let statusClass = '';
        
        if (product.stock === 0) {
            stockStatus = 'Stok Dışı';
            statusClass = 'status-out-of-stock';
        } else if (product.stock <= settings.lowStockThreshold) {
            stockStatus = 'Kritik Stok';
            statusClass = 'status-low-stock';
        } else {
            stockStatus = 'Stokta';
            statusClass = 'status-in-stock';
        }
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-3 px-4">${product.code}</td>
            <td class="py-3 px-4">${product.name}</td>
            <td class="py-3 px-4">${categoryName}</td>
            <td class="py-3 px-4">${product.stock} ${product.unit}</td>
            <td class="py-3 px-4">${product.unit}</td>
            <td class="py-3 px-4">
                <span class="status-badge ${statusClass}">${stockStatus}</span>
            </td>
            <td class="py-3 px-4">
                <div class="flex space-x-2">
                    <button class="edit-product-btn text-indigo-600 hover:text-indigo-900" data-id="${product.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-product-btn text-red-600 hover:text-red-900" data-id="${product.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;
        productsTable.appendChild(row);
    });

    // Add event listeners to edit and delete buttons
    document.querySelectorAll('.edit-product-btn').forEach(button => {
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            openEditProductModal(productId);
        });
    });
    
    document.querySelectorAll('.delete-product-btn').forEach(button => {
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            openDeleteConfirmationModal('product', productId);
        });
    });
}

// Render categories table
function renderCategoriesTable() {
    categoriesTable.innerHTML = '';
    
    if (categories.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">Henüz kategori bulunmuyor</td>
        `;
        categoriesTable.appendChild(emptyRow);
        return;
    }
    
    categories.forEach(category => {
        // Count products in this category
        const productCount = products.filter(product => product.categoryId === category.id).length;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-3 px-4">${category.id}</td>
            <td class="py-3 px-4">${category.name}</td>
            <td class="py-3 px-4">${category.description || '-'}</td>
            <td class="py-3 px-4">${productCount}</td>
            <td class="py-3 px-4">
                <div class="flex space-x-2">
                    <button class="edit-category-btn text-indigo-600 hover:text-indigo-900" data-id="${category.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-category-btn text-red-600 hover:text-red-900" data-id="${category.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;
        categoriesTable.appendChild(row);
    });
    
    // Add event listeners to edit and delete buttons
    document.querySelectorAll('.edit-category-btn').forEach(button => {
        button.addEventListener('click', () => {
            const categoryId = button.getAttribute('data-id');
            openEditCategoryModal(categoryId);
        });
    });
    
    document.querySelectorAll('.delete-category-btn').forEach(button => {
        button.addEventListener('click', () => {
            const categoryId = button.getAttribute('data-id');
            openDeleteConfirmationModal('category', categoryId);
        });
    });
}

// Render transactions table
function renderTransactionsTable(startDate = '', endDate = '', type = '', filteredTransactions = null) {
    transactionsTable.innerHTML = '';

    if (transactions.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">Henüz işlem bulunmuyor</td>
        `;
        transactionsTable.appendChild(emptyRow);
        return;
    }

    // Önce tarihe göre sırala, sonra aynı tarihtekileri ID'ye göre sırala
    const sortedTransactions = [...transactions].sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        if (dateB - dateA === 0) {
            return parseInt(b.id.replace(/\D/g, '')) - parseInt(a.id.replace(/\D/g, ''));
        }
        return dateB - dateA;
    });

    // ID'leri 1'den başlayarak yeniden numaralandır
    sortedTransactions.forEach((transaction, index) => {
        const rowNumber = transactions.length - index;
        const product = products.find(p => p.id === transaction.productId);
        const productName = product ? product.name : 'Silinmiş Ürün';
        
        // Tarih formatını ayarla (24 saat formatı)
        const transactionDate = new Date(transaction.date);
        const day = transactionDate.getDate().toString().padStart(2, '0');
        const month = (transactionDate.getMonth() + 1).toString().padStart(2, '0');
        const year = transactionDate.getFullYear();
        const hours = transactionDate.getHours().toString().padStart(2, '0');
        const minutes = transactionDate.getMinutes().toString().padStart(2, '0');
        const formattedDate = `${day}.${month}.${year} ${hours}:${minutes}`;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-3 px-4">${rowNumber}</td>
            <td class="py-3 px-4">${formattedDate}</td>
            <td class="py-3 px-4">${productName}</td>
            <td class="py-3 px-4">
                <span class="${transaction.type === 'in' ? 'text-green-600' : 'text-red-600'}">
                    ${transaction.type === 'in' ? 'Giriş' : 'Çıkış'}
                </span>
            </td>
            <td class="py-3 px-4">${transaction.quantity} ${product ? product.unit : ''}</td>
            <td class="py-3 px-4">${transaction.note || '-'}</td>
        `;
        transactionsTable.appendChild(row);
    });
}

// Filter transactions based on date range, type, and product
function filterTransactions(searchTerm = '') {
    const startDateInput = document.getElementById('filter-transaction-start-date');
    const endDateInput = document.getElementById('filter-transaction-end-date');
    const typeSelect = document.getElementById('filter-transaction-type');
    
    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';
    const type = typeSelect ? typeSelect.value : '';
    
    const filteredTransactions = transactions.filter(transaction => {
        const product = products.find(p => p.id === transaction.productId);
        const productName = product ? product.name.toLowerCase() : '';
        const transactionId = transaction.id.toLowerCase();

        // Search filter
        const matchesSearch = !searchTerm || 
            productName.includes(searchTerm) || 
            transactionId.includes(searchTerm);

        // Date filter        
        const transactionDate = new Date(transaction.date);
        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;
        const matchesDate = (!start || transactionDate >= start) && (!end || transactionDate <= end);

        // Type filter
        const matchesType = !type || transaction.type === type;

        return matchesSearch && matchesDate && matchesType;
    });

    renderTransactionsTable('', '', '', filteredTransactions);
}

// Add searchTerm parameter to filterProducts function
function filterProducts(searchTerm = '') {
    // If no searchTerm provided, get it from the search input
    if (!searchTerm) {
        const searchInput = document.getElementById('search-product');
        searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    }

    const categoryFilter = document.getElementById('filter-category').value;
    const stockFilter = document.getElementById('filter-stock').value;

    // Filter products
    const filteredProducts = products.filter(product => {
        // Search filter - check both code and name
        const matchesSearch = searchTerm === '' || 
            product.code.toLowerCase().includes(searchTerm) || 
            product.name.toLowerCase().includes(searchTerm);
        
        // Category filter
        const matchesCategory = categoryFilter === 'all' || categoryFilter === product.categoryId.toString();

        // Stock filter
        let matchesStock = true;
        if (stockFilter === 'in-stock') {
            matchesStock = product.stock > settings.lowStockThreshold;
        } else if (stockFilter === 'low-stock') {
            matchesStock = product.stock > 0 && product.stock <= settings.lowStockThreshold;
        } else if (stockFilter === 'out-of-stock') {
            matchesStock = product.stock === 0;
        }

        return matchesSearch && matchesCategory && matchesStock;
    });

    // Clear and repopulate the table with filtered results
    const productsTable = document.getElementById('products-table');
    if (productsTable) {
        productsTable.innerHTML = '';
        
        if (filteredProducts.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">Ürün bulunamadı</td>
            `;
            productsTable.appendChild(emptyRow);   
            return;
        }
        
        renderProductsList(filteredProducts);
    }
}

// Add new function to render filtered products
function renderProductsList(productsList) {
    const productsTable = document.getElementById('products-table');
    if (!productsTable) return;

    productsList.forEach(product => {
        const category = categories.find(c => c.id === product.categoryId);
        const categoryName = category ? category.name : 'Kategori Yok';
        
        let stockStatus = '';
        let statusClass = '';
        
        if (product.stock === 0) {
            stockStatus = 'Stok Dışı';
            statusClass = 'status-out-of-stock';
        } else if (product.stock <= settings.lowStockThreshold) {
            stockStatus = 'Kritik Stok';
            statusClass = 'status-low-stock';
        } else {
            stockStatus = 'Stokta';
            statusClass = 'status-in-stock';
        }
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="py-3 px-4">${product.code}</td>
            <td class="py-3 px-4">${product.name}</td>
            <td class="py-3 px-4">${categoryName}</td>
            <td class="py-3 px-4">${product.stock} ${product.unit}</td>
            <td class="py-3 px-4">${product.unit}</td>
            <td class="py-3 px-4">
                <span class="status-badge ${statusClass}">${stockStatus}</span>
            </td>
            <td class="py-3 px-4">
                <div class="flex space-x-2">
                    <button class="edit-product-btn text-indigo-600 hover:text-indigo-900" data-id="${product.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-product-btn text-red-600 hover:text-red-900" data-id="${product.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </td>
        `;
        productsTable.appendChild(row);
    });

    // Reattach event listeners to new buttons
    document.querySelectorAll('.edit-product-btn').forEach(button => {
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            openEditProductModal(productId);
        });
    });
    
    document.querySelectorAll('.delete-product-btn').forEach(button => {
        button.addEventListener('click', () => {
            const productId = button.getAttribute('data-id');
            openDeleteConfirmationModal('product', productId);
        });
    });
}

// Update category dropdowns
function updateCategoryDropdowns() {
    const categoryDropdowns = [
        document.getElementById('filter-category'),
        document.getElementById('product-category'),
        document.getElementById('edit-product-category')
    ];
    
    categoryDropdowns.forEach(dropdown => {
        if (!dropdown) return;
        
        // Clear existing options except the first one
        while (dropdown.options.length > 1) {
            dropdown.remove(1);
        }
        
        // Add category options
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            dropdown.appendChild(option);
        });
    });
}

// Update product dropdowns
function updateProductDropdowns() {
    const productDropdowns = [
        document.getElementById('transaction-product'),
        document.getElementById('stock-product'),
        document.getElementById('remove-stock-product')
    ];
    
    productDropdowns.forEach(dropdown => {
        if (!dropdown) return;
        
        // Clear existing options except the first one
        while (dropdown.options.length > 1) {
            dropdown.remove(1);
        }
        
        // Add product options
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.code} - ${product.name}`;
            dropdown.appendChild(option);
        });
    });
}

// Add this new function after other helper functions
function generateProductCode() {
    // En büyük numarayı bul, bir artır
    const prefix = 'PRD';
    const numbers = products
        .map(p => parseInt((p.code || '').replace(prefix, '')))
        .filter(n => !isNaN(n));
    const maxNum = numbers.length > 0 ? Math.max(...numbers) : 0;
    return `${prefix}${(maxNum + 1).toString().padStart(3, '0')}`;
}

// Add this new function for sequential category IDs
function generateCategoryId() {
    if (categories.length === 0) {
        return 'kat1';
    }
    
    const maxId = categories
        .map(c => parseInt(c.id.replace('kat', '')))
        .reduce((max, current) => Math.max(max, current), 0);
    
    return `kat${maxId + 1}`;
}

// Yeni saveData yerine, veri ekleme/güncelleme fonksiyonları API çağrıları kullanır.
// Örneğin, handleAddProduct fonksiyonunun güncellenmiş hali:
function handleAddProduct(e) {
    e.preventDefault();

    const code = generateProductCode();
    const name = document.getElementById('product-name').value.trim().toUpperCase();
    const categoryId = document.getElementById('product-category').value;
    const stock = parseInt(document.getElementById('product-stock').value);
    const unit = document.getElementById('product-unit').value;
    const description = document.getElementById('product-description').value;
    const price = parseFloat(document.getElementById('product-price').value) || 0;

    if (!name || !categoryId || isNaN(stock)) {
        showNotification('Lütfen tüm alanları doldurun', 'error');
        return;
    }

    // Aynı isimde ürün kontrolü (büyük/küçük harf ayrımı olmadan)
    if (products.some(product => product.name.toUpperCase() === name)) {
        showNotification('Aynı isimde bir ürün zaten mevcut!', 'error');
        return;
    }

    const newProduct = {
        id: generateId(),
        code,
        name,
        categoryId,
        stock,
        unit,
        description,
        price
    };

    fetch(`${apiUrl}/api/products`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProduct)
    })
    .then(res => res.json())
    .then(data => {
        showNotification('Ürün başarıyla eklendi', 'success');
        loadData();
        closeModal(document.getElementById('add-product-modal'));
    })
    .catch(err => {
        console.error(err);
        showNotification('Ürün eklenirken hata oluştu', 'error');
    });
}

function handleAddCategory(e) {
    e.preventDefault();
    
    const nameInput = document.getElementById('category-name');
    const name = nameInput ? nameInput.value.trim() : '';
    const description = document.getElementById('category-description').value;

    if (!name) {
        showNotification('Kategori adı boş olamaz', 'error');
        return;
    }

    const newCategory = {
        id: generateCategoryId(),
        name,
        description
    };

    fetch(`${apiUrl}/api/categories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCategory)
    })
    .then(res => res.json())
    .then(data => {
        categories.push(data);
        showNotification('Kategori başarıyla eklendi', 'success');
        renderCategoriesTable();
        closeModal(document.getElementById('add-category-modal'));
        document.getElementById('add-category-form').reset();
    })
    .catch(err => {
        console.error('Kategori ekleme hatası:', err);
        showNotification('Kategori eklenirken hata oluştu', 'error');
    });
}

function handleAddStock(e) {
    e.preventDefault();
    
    const form = e.target;
    const modal = document.getElementById('add-stock-modal');
    
    const productId = form.querySelector('#stock-product').value;
    const quantity = parseInt(form.querySelector('#stock-quantity').value);
    const note = form.querySelector('#stock-note').value;
    
    const transaction = {
        id: generateId(),
        date: new Date().toISOString(), // Otomatik olarak şu anki tarih ve saat
        productId,
        type: 'in',
        quantity,
        note
    };
    
    fetch(`${apiUrl}/api/transactions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transaction)
    })
    .then(res => {
        if (!res.ok) return res.json().then(err => Promise.reject(err));
        return res.json;
    })
    .then(data => {
        showNotification('Stok girişi başarıyla kaydedildi', 'success');
        loadData();
        closeModal(modal);
        form.reset();
    })
    .catch(err => {
        console.error('Error:', err);
        showNotification(err.message || 'Stok girişi yapılırken hata oluştu', 'error');
    });
}

function handleRemoveStock(e) {
    e.preventDefault();
    
    const form = e.target;
    const modal = document.getElementById('remove-stock-modal');
    
    const productId = form.querySelector('#remove-stock-product').value;
    const quantity = parseInt(form.querySelector('#remove-stock-quantity').value);
    const note = form.querySelector('#remove-stock-note').value;
    
    const transaction = {
        id: generateId(),
        date: new Date().toISOString(), // Otomatik olarak şu anki tarih ve saat
        productId,
        type: 'out',
        quantity,
        note
    };
    
    fetch(`${apiUrl}/api/transactions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transaction)
    })
    .then(res => {
        if (!res.ok) return res.json().then(err => Promise.reject(err));
        return res.json;
    })
    .then(data => {
        showNotification('Stok çıkışı başarıyla kaydedildi', 'success');
        loadData();
        closeModal(modal);
        form.reset();
    })
    .catch(err => {
        console.error('Error:', err);
        showNotification(err.message || 'Stok çıkışı yapılırken hata oluştu', 'error');
    });
}
    
// Open edit product modal
function openEditProductModal(productId) {
    const modal = document.getElementById('edit-product-modal');
    if (!modal) return;

    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('Ürün bulunamadı', 'error');
        return;
    }

    // Update category dropdown before setting values
    updateCategoryDropdowns();
    
    const idInput = modal.querySelector('#edit-product-id');
    const codeInput = modal.querySelector('#edit-product-code');
    const nameInput = modal.querySelector('#edit-product-name');
    const categorySelect = modal.querySelector('#edit-product-category');
    const priceInput = modal.querySelector('#edit-product-price');
    const unitSelect = modal.querySelector('#edit-product-unit');
    const descInput = modal.querySelector('#edit-product-description');
    
    if (idInput) idInput.value = product.id;
    if (codeInput) codeInput.value = product.code;
    if (nameInput) nameInput.value = product.name;
    if (categorySelect) categorySelect.value = product.categoryId;
    if (priceInput) priceInput.value = product.price;
    if (unitSelect) unitSelect.value = product.unit;
    if (descInput) descInput.value = product.description || '';

    openModal(modal);
}

// Handle editing a product
function handleEditProduct(e) {
    e.preventDefault();

    const modal = document.getElementById('edit-product-modal');
    const form = e.target;

    const id = form.querySelector('#edit-product-id').value;
    const code = form.querySelector('#edit-product-code').value;
    const name = form.querySelector('#edit-product-name').value;
    const categoryId = form.querySelector('#edit-product-category').value;
    const price = parseFloat(form.querySelector('#edit-product-price').value);
    const unit = form.querySelector('#edit-product-unit').value;
    const description = form.querySelector('#edit-product-description').value;

    // Validation
    if (!code || !name || !categoryId || isNaN(price)) {
        showNotification('Lütfen tüm alanları doldurun', 'error');
        return;
    }

    // Check if code exists (excluding current product)
    if (products.some(p => p.id !== id && p.code.toLowerCase() === code.toLowerCase())) {
        showNotification('Bu ürün kodu zaten kullanılıyor', 'error');
        return;
    }

    // API'ye PATCH/PUT ile güncelleme isteği gönder
    fetch(`http://localhost:3000/api/products/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            code,
            name,
            categoryId,
            price,
            unit,
            description
        })
    })
    .then(res => {
        if (!res.ok) throw new Error('Güncelleme başarısız');
        return res.json();
    })
    .then(data => {
        showNotification('Ürün başarıyla güncellendi', 'success');
        closeModal(modal);
        loadData();
    })
    .catch(err => {
        console.error(err);
        showNotification('Ürün güncellenirken hata oluştu', 'error');
    });
}

// Open edit category modal
function openEditCategoryModal(categoryId) {
    const modal = document.getElementById('edit-category-modal');
    if (!modal) return;

    const category = categories.find(c => c.id === categoryId);
    if (!category) return;
    
    const idInput = modal.querySelector('#edit-category-id');
    const nameInput = modal.querySelector('#edit-category-name');
    const descInput = modal.querySelector('#edit-category-description');
    
    if (idInput) idInput.value = category.id;
    if (nameInput) nameInput.value = category.name;
    if (descInput) descInput.value = category.description || '';

    openModal(modal);

    // Add form submit handler
    const form = modal.querySelector('#edit-category-form');
    if (form) {
        form.onsubmit = handleEditCategory;
    }
}

// Handle editing a category
function handleEditCategory(e) {
    e.preventDefault();
    
    const form = e.target;
    const modal = document.getElementById('edit-category-modal');
    
    const id = form.querySelector('#edit-category-id').value;
    const name = form.querySelector('#edit-category-name').value;
    const description = form.querySelector('#edit-category-description').value;

    // Find the category
    const category = categories.find(c => c.id === id);
    if (!category) {
        showNotification('Kategori bulunamadı.', 'error');
        return;
    }

    // Update category data
    category.name = name;
    category.description = description;
    
    // Save data
    saveData();
    
    // Update UI
    closeModal(modal);
    renderCategoriesTable();
    updateCategoryDropdowns();
    
    // Show success message
    showNotification('Kategori başarıyla güncellendi', 'success');
}

// Open delete confirmation modal
function openDeleteConfirmationModal(type, id) {
    const modal = document.getElementById('delete-confirmation-modal');
    if (!modal) return;

    const typeSpan = modal.querySelector('.delete-confirmation-type');
    const idInput = modal.querySelector('.delete-confirmation-id');
    const typeInput = modal.querySelector('.delete-confirmation-type');
    
    if (typeSpan) typeSpan.textContent = type === 'product' ? 'ürünü' : 'kategoriyi';
    if (idInput) idInput.value = id;
    if (typeInput) typeInput.value = type;

    openModal(modal);
}

// Handle delete confirmation
function handleDeleteConfirmation(e) {
    e.preventDefault();

    const modal = document.getElementById('delete-confirmation-modal');
    const id = modal.querySelector('.delete-confirmation-id').value;
    const type = modal.querySelector('.delete-confirmation-type').value;

    if (type === 'category') {
        // Önce ürün kontrolü yap
        const hasProducts = products.some(p => p.categoryId === id);
        if (hasProducts) {
            showNotification('Bu kategoriye ait ürünler bulunmaktadır. Önce ürünleri silmelisiniz.', 'error');
            closeModal(modal);
            return;
        }

        // API üzerinden kategoriyi sil
        fetch(`${apiUrl}/api/categories/${id}`, {
            method: 'DELETE'
        })
        .then(res => {
            if (!res.ok) throw new Error('Silme işlemi başarısız');
            return res.json();
        })
        .then(data => {
            showNotification('Kategori başarıyla silindi', 'success');
            loadData(); // Verileri sunucudan tekrar yükle
            closeModal(modal);
        })
        .catch(err => {
            console.error('Error:', err);
            showNotification('Kategori silinirken hata oluştu', 'error');
        });
        return;
    }

    if (type === 'product') {
        // Find the product
        const product = products.find(p => p.id === id);
        if (!product) {
            showNotification('Ürün bulunamadı.', 'error');
            return;
        }

        // Check if product has stock
        if (product.stock > 0) {
            showNotification('Bu ürünün stoğu bulunmaktadır. Önce stoğu sıfırlayın.', 'error');
            closeModal(modal);
            return;
        }

        // API üzerinden ürünü sil
        fetch(`${apiUrl}/api/products/${id}`, {
            method: 'DELETE'
        })
        .then(res => {
            if (!res.ok) throw new Error('Silme işlemi başarısız');
            return res.json();
        })
        .then(data => {
            showNotification(`${product.name} ürünü başarıyla silindi`, 'success');
            loadData(); // Verileri sunucudan tekrar yükle
            closeModal(modal);
        })
        .catch(err => {
            console.error('Error:', err);
            showNotification('Ürün silinirken hata oluştu', 'error');
        });
    }
}

// Save settings
function saveSettings() {
    const companyName = document.getElementById('company-name').value;
    const lowStockThreshold = parseInt(document.getElementById('low-stock-threshold').value);
    const currency = document.getElementById('currency').value;
    const dateFormat = document.getElementById('date-format').value;
    
    settings.companyName = companyName;
    settings.lowStockThreshold = lowStockThreshold;
    settings.currency = currency;
    settings.dateFormat = dateFormat;

    // Save data
    saveData();
    
    // Update UI
    updateDashboard();
    
    // Show success message
    showNotification('Ayarlar başarıyla kaydedildi', 'success');
}

// Load settings
function loadSettings() {
    document.getElementById('company-name').value = settings.companyName;
    document.getElementById('low-stock-threshold').value = settings.lowStockThreshold;
    document.getElementById('currency').value = settings.currency;
    document.getElementById('date-format').value = settings.dateFormat;
}

// Generate a unique ID
function generateId() {
    return '_' + Math.random().toString(36).substr(2, 9);
}

// Format date
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('tr-TR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

// Format currency
function formatCurrency(amount) {
    return `${currencySymbols[settings.currency]}${amount.toFixed(2)}`;
}

// Show notification
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type} fixed bottom-4 right-4 p-4 rounded-lg shadow-lg text-white`;
    notification.style.backgroundColor = type === 'success' ? '#059669' : '#DC2626';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Remove reference to stock-value-chart as it does not exist in reports.html
function initializeCharts() {
    // Destroy existing charts if they exist
    if (categoryDistributionChart) categoryDistributionChart.destroy();
    if (stockMovementChart) stockMovementChart.destroy();

    const categoryDistributionCtx = document.getElementById('category-distribution-chart');
    const stockMovementCtx = document.getElementById('stock-movement-chart');
    const productListCtx = document.getElementById('product-list-chart');
    const stockInListCtx = document.getElementById('stock-in-list-chart');
    const stockOutListCtx = document.getElementById('stock-out-list-chart');

    if (!categoryDistributionCtx || !stockMovementCtx || !productListCtx || !stockInListCtx || !stockOutListCtx) {
        console.warn('One or more chart elements not found');
        return;
    }

    // Ensure all existing charts are destroyed before creating new ones
    if (stockMovementChart) stockMovementChart.destroy();
    if (categoryDistributionChart) categoryDistributionChart.destroy();
    if (productListChart) productListChart.destroy();
    if (stockInListChart) stockInListChart.destroy();
    if (stockOutListChart) stockOutListChart.destroy();

    // Stock Movement Chart
    stockMovementChart = new Chart(stockMovementCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: transactions.map(t => formatDate(t.date)),
            datasets: [{
                label: 'Stok Hareketi',
                data: transactions.map(t => t.type === 'in' ? t.quantity : -t.quantity),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Miktar'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Tarih'
                    }
                }
            }
        }
    });

    // Product List Chart
    productListChart = new Chart(productListCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: products.map(p => p.name),
            datasets: [{
                label: 'Ürünler',
                data: products.map(p => p.stock),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Stok Miktarı'
                    }
                }
            }
        }
    });

    // Stock In List Chart
    stockInListChart = new Chart(stockInListCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: transactions.filter(t => t.type === 'in').map(t => formatDate(t.date)),
            datasets: [{
                label: 'Stok Girişleri',
                data: transactions.filter(t => t.type === 'in').map(t => t.quantity),
                borderColor: 'rgba(54, 162, 235, 1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Miktar'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Tarih'
                    }
                }
            }
        }
    });

    // Stock Out List Chart
    stockOutListChart = new Chart(stockOutListCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: transactions.filter(t => t.type === 'out').map(t => formatDate(t.date)),
            datasets: [{
                label: 'Stok Çıkışları',
                data: transactions.filter(t => t.type === 'out').map(t => t.quantity),
                borderColor: 'rgba(255, 99, 132, 1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Miktar'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Tarih'
                    }
                }
            }
        }
    });

    // Category Distribution Chart
    categoryDistributionChart = new Chart(categoryDistributionCtx.getContext('2d'), {
        type: 'pie',
        data: {
            labels: categories.map(c => c.name),
            datasets: [{
                data: categories.map(c => 
                    products.filter(p => p.categoryId === c.id).length
                ),
                backgroundColor: categories.map((_, index) => 
                    `hsl(${index * 360 / categories.length}, 70%, 50%)`
                ),
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                }
            }
        }
    });
}

// Page navigation function
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            // Doğrudan sayfaya yönlendir
            window.location.href = href;
        });
    });
    
    // Aktif sayfayı vurgula
    const currentPage = window.location.pathname.split('/').pop();
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage) {
            link.classList.add('bg-indigo-600');
        } else {
            link.classList.remove('bg-indigo-600');
        }
    });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    loadData();
    initializeUI();
    setupEventListeners();
    setupNavigation();
    
    // Sayfa yüklendiğinde ilgili işlevleri çalıştır
    const currentPage = window.location.pathname.split('/').pop();
    const pageName = currentPage.replace('.html', '');
    
    switch(pageName) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'products':
            renderProductsTable();
            break;
        case 'categories':
            renderCategoriesTable();
            break;
        case 'transactions':
            renderTransactionsTable();
            break;
        case 'reports':
            initializeCharts();
            break;
        case 'settings':
            loadSettings();
            break;
    }
    
    // Uygulamayı file:// üzerinden çalıştırmayın; yerel bir sunucu kullanın (örn. Live Server)
    if (window.location.protocol === 'file:') {
        console.warn('Uygulamayı file:// üzerinden çalıştırmayın; yerel bir sunucu kullanın (örn. Live Server)');
    }
});

function exportToExcel() {
    // Prepare the data
    const productsData = products.map(product => {
        const category = categories.find(c => c.id === product.categoryId);
        return {
            'Ürün Kodu': product.code,
            'Ürün Adı': product.name,
            'Kategori': category ? category.name : '-',
            'Stok': product.stock,
            'Birim': product.unit,
            'Birim Fiyat': product.price,
            'Toplam Değer': product.stock * product.price,
            'Durum': product.stock === 0 ? 'Stok Dışı' : 
                     product.stock <= settings.lowStockThreshold ? 'Kritik Stok' : 'Stokta',
        };
    });

    // Convert to worksheet
    const ws = XLSX.utils.json_to_sheet(productsData);

    // Create workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Ürün Listesi");
    
    // Generate Excel file
    const fileName = `urun-listesi-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    
    showNotification('Excel dosyası başarıyla oluşturuldu', 'success');
    
    // Add debugging logs to verify data population
    console.log('Products:', products);
    console.log('Categories:', categories);
    console.log('Transactions:', transactions);
}

// --- Raporlar sayfası için hareketleri Excel'e aktarma ---
function exportTransactionsToExcel() {
    // Hareketleri (transactions) Excel'e aktar
    const transactionsData = transactions.map((t, idx) => {
        const product = products.find(p => p.id === t.productId);
        return {
            'Sıra': idx + 1,
            'Tarih': formatDate(t.date),
            'Ürün': product ? product.name : 'Silinmiş Ürün',
            'Tür': t.type === 'in' ? 'Giriş' : 'Çıkış',
            'Miktar': t.quantity,
            'Açıklama': t.note || ''
        };
    });
    const ws = XLSX.utils.json_to_sheet(transactionsData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Hareketler');
    const fileName = `stok-hareketleri-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    showNotification('Hareketler Excel olarak indirildi', 'success');
}

// --- Hareket eklerken otomatik T numarası ---
function generateTransactionId() {
    const prefix = 'T';
    const numbers = transactions
        .map(t => parseInt((t.id || '').replace(prefix, '')))
        .filter(n => !isNaN(n));
    const maxNum = numbers.length > 0 ? Math.max(...numbers) : 0;
    return `${prefix}${maxNum + 1}`;
}
