using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business.Services
{
    public interface IExcelExportService
    {
        Task<byte[]> ExportMembersToExcelAsync();
        Task<byte[]> ExportBooksToExcelAsync();
        Task<byte[]> ExportBorrowedBooksToExcelAsync();
        Task<byte[]> ExportPopularBooksToExcelAsync(int count = 10);
        Task<byte[]> ExportLateReturnsToExcelAsync();
    }
}
