using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class MemberRepository : IMemberRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public MemberRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public Member? GetById(int id)
        {
            return _context.Members.Find(id);
        }

        public async Task<Member?> GetByIdAsync(int id)
        {
            return await _context.Members.FindAsync(id);
        }

        public IEnumerable<Member> GetAll()
        {
            return _context.Members.ToList();
        }

        public async Task<IEnumerable<Member>> GetAllAsync()
        {
            return await _context.Members.ToListAsync();
        }

        public IEnumerable<Member> Find(Expression<Func<Member, bool>> predicate)
        {
            return _context.Members.Where(predicate).ToList();
        }

        public async Task<IEnumerable<Member>> FindAsync(Expression<Func<Member, bool>> predicate)
        {
            return await _context.Members.Where(predicate).ToListAsync();
        }

        public Member? FirstOrDefault(Expression<Func<Member, bool>> predicate)
        {
            return _context.Members.FirstOrDefault(predicate);
        }

        public async Task<Member?> FirstOrDefaultAsync(Expression<Func<Member, bool>> predicate)
        {
            return await _context.Members.FirstOrDefaultAsync(predicate);
        }

        public void Add(Member member)
        {
            _context.Members.Add(member);
            _context.SaveChanges();
        }

        public async Task AddAsync(Member member)
        {
            await _context.Members.AddAsync(member);
            await _context.SaveChangesAsync();
        }

        public void AddRange(IEnumerable<Member> members)
        {
            _context.Members.AddRange(members);
            _context.SaveChanges();
        }

        public async Task AddRangeAsync(IEnumerable<Member> members)
        {
            await _context.Members.AddRangeAsync(members);
            await _context.SaveChangesAsync();
        }

        public void Update(Member member)
        {
            _context.Members.Update(member);
            _context.SaveChanges();
        }

        public void Remove(Member member)
        {
            _context.Members.Remove(member);
            _context.SaveChanges();
        }

        public void RemoveRange(IEnumerable<Member> members)
        {
            _context.Members.RemoveRange(members);
            _context.SaveChanges();
        }

        public int Count()
        {
            return _context.Members.Count();
        }

        public async Task<int> CountAsync()
        {
            return await _context.Members.CountAsync();
        }

        public int Count(Expression<Func<Member, bool>> predicate)
        {
            return _context.Members.Count(predicate);
        }

        public async Task<int> CountAsync(Expression<Func<Member, bool>> predicate)
        {
            return await _context.Members.CountAsync(predicate);
        }

        public bool Exists(Expression<Func<Member, bool>> predicate)
        {
            return _context.Members.Any(predicate);
        }

        public async Task<bool> ExistsAsync(Expression<Func<Member, bool>> predicate)
        {
            return await _context.Members.AnyAsync(predicate);
        }

        public Member? GetByTCKN(string tckn)
        {
            return _context.Members.FirstOrDefault(m => m.TCKN == tckn);
        }

        public async Task<Member?> GetByTCKNAsync(string tckn)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.TCKN == tckn);
        }

        public Member? GetByEmail(string email)
        {
            return _context.Members.FirstOrDefault(m => m.Email == email);
        }

        public async Task<Member?> GetByEmailAsync(string email)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.Email == email);
        }

        public IEnumerable<Member> GetAllWithLoans()
        {
            return _context.Members.Include(m => m.Loans).ToList();
        }

        public async Task<IEnumerable<Member>> GetAllWithLoansAsync()
        {
            return await _context.Members.Include(m => m.Loans).ToListAsync();
        }
    }
}