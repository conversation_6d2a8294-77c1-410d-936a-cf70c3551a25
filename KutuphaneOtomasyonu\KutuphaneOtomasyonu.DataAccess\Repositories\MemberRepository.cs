using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class MemberRepository : IMemberRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public MemberRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Member> GetAll()
        {
            return _context.Members.ToList();
        }

        public async Task<IEnumerable<Member>> GetAllAsync()
        {
            return await _context.Members.ToListAsync();
        }

        public Member GetById(int id)
        {
            return _context.Members.FirstOrDefault(m => m.Id == id);
        }

        public async Task<Member> GetByIdAsync(int id)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.Id == id);
        }

        public void Add(Member member)
        {
            _context.Members.Add(member);
            _context.SaveChanges();
        }

        public async Task AddAsync(Member member)
        {
            await _context.Members.AddAsync(member);
            await _context.SaveChangesAsync();
        }

        public void Update(Member member)
        {
            _context.Members.Update(member);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var member = _context.Members.FirstOrDefault(m => m.Id == id);
            if (member != null)
            {
                _context.Members.Remove(member);
                _context.SaveChanges();
            }
        }

        public Member GetByEmail(string email)
        {
            return _context.Members.FirstOrDefault(m => m.Email == email);
        }

        public async Task<Member> GetByEmailAsync(string email)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.Email == email);
        }
    }
}