using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class MemberRepository : IMemberRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public MemberRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Member> GetAll()
        {
            return _context.Members.ToList();
        }

        public async Task<IEnumerable<Member>> GetAllAsync()
        {
            return await _context.Members.ToListAsync();
        }

        public Member GetById(int id)
        {
            return _context.Members.FirstOrDefault(m => m.Id == id);
        }

        public async Task<Member> GetByIdAsync(int id)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.Id == id);
        }

        public void Add(Member member)
        {
            _context.Members.Add(member);
            _context.SaveChanges();
        }

        public async Task AddAsync(Member member)
        {
            await _context.Members.AddAsync(member);
            await _context.SaveChangesAsync();
        }

        public void Update(Member member)
        {
            _context.Members.Update(member);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var member = _context.Members.FirstOrDefault(m => m.Id == id);
            if (member != null)
            {
                _context.Members.Remove(member);
                _context.SaveChanges();
            }
        }

        public Member GetByContactInfo(string contactInfo)
        {
            return _context.Members.FirstOrDefault(m => m.ContactInfo == contactInfo);
        }

        public async Task<Member> GetByContactInfoAsync(string contactInfo)
        {
            return await _context.Members.FirstOrDefaultAsync(m => m.ContactInfo == contactInfo);
        }

        // IRepository<Member> methods
        public IEnumerable<Member> Find(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return _context.Members.Where(predicate).ToList();
        }

        public async Task<IEnumerable<Member>> FindAsync(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return await _context.Members.Where(predicate).ToListAsync();
        }

        public Member FirstOrDefault(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return _context.Members.FirstOrDefault(predicate);
        }

        public async Task<Member> FirstOrDefaultAsync(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return await _context.Members.FirstOrDefaultAsync(predicate);
        }

        public void Remove(Member entity)
        {
            _context.Members.Remove(entity);
            _context.SaveChanges();
        }

        public void RemoveRange(IEnumerable<Member> entities)
        {
            _context.Members.RemoveRange(entities);
            _context.SaveChanges();
        }

        public int Count(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return _context.Members.Count(predicate);
        }

        public async Task<int> CountAsync(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return await _context.Members.CountAsync(predicate);
        }

        public bool Exists(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return _context.Members.Any(predicate);
        }

        public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate)
        {
            return await _context.Members.AnyAsync(predicate);
        }

        public void AddRange(IEnumerable<Member> entities)
        {
            _context.Members.AddRange(entities);
            _context.SaveChanges();
        }

        public async Task AddRangeAsync(IEnumerable<Member> entities)
        {
            await _context.Members.AddRangeAsync(entities);
            await _context.SaveChangesAsync();
        }
    }
}