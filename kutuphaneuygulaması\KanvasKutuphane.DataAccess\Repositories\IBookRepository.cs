using KanvasKutuphane.Entities.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.DataAccess
{
    public interface IBookRepository
    {
        Task<IEnumerable<Book>> GetAllAsync();
        Task<Book> GetByIdAsync(int id);
        Task AddAsync(Book book);
        Task UpdateAsync(Book book);
        Task DeleteAsync(int id);
        Task<int> CountAsync();
    }
}
