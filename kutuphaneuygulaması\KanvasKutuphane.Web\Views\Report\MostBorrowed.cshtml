@model List<KanvasKutuphane.Business.ViewModels.MostBorrowedBookViewModel>
@{
    ViewData["Title"] = "En Çok Ödünç Alınan Kitaplar";
}

<h2 class="text-2xl font-bold mb-4">En Çok Ödünç Alınan Kitaplar</h2>

<table class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="px-4 py-2 border-b">Kitap Adı</th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON></th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="px-4 py-2 border-b">@item.Title</td>
                <td class="px-4 py-2 border-b">@item.Author</td>
                <td class="px-4 py-2 border-b">@item.BorrowCount</td>
            </tr>
        }
    </tbody>
</table>
