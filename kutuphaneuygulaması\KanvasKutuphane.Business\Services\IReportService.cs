using KanvasKutuphane.Entities.Models;
using KanvasKutuphane.Entities.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business.Services
{
    public interface IReportService
    {
        Task<List<PopularBookViewModel>> GetMostBorrowedBooksAsync(int count);
        Task<List<LateReturnViewModel>> GetLateReturnsAsync();
        Task<List<MemberBorrowingHistoryViewModel>> GetBorrowingHistoryByMemberIdAsync(int memberId);
    }
}
