using KanvasKutuphane.Entities.Models;
using KanvasKutuphane.Business.ViewModels; // Business ViewModels kullanıyoruz
using KanvasKutuphane.Entities.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business.Services
{
    public interface IReportService
    {
        Task<List<Business.ViewModels.PopularBookViewModel>> GetMostBorrowedBooksAsync(int count);
        Task<List<Business.ViewModels.LateReturnViewModel>> GetLateReturnsAsync();
        Task<List<Entities.ViewModels.MemberBorrowingHistoryViewModel>> GetBorrowingHistoryByMemberIdAsync(int memberId);
    }
}
