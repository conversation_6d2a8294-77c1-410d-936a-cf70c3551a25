/* Modern UI Variables */
:root {
    --primary: #4f46e5;
    --primary-dark: #3730a3;
    --success: #10b981;
    --danger: #ef4444;
    --warning: #f59e0b;
    --info: #3b82f6;
    --background: #f8fafc;
    --surface: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #64748b;
    --border: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-lg: 0.75rem;
    --transition: all 0.2s ease;
}

/* Base Styles */
body {
    font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", <PERSON>o, sans-serif;
    background: var(--background);
    color: var(--text-primary);
    line-height: 1.5;
}

html {
    scroll-behavior: smooth;
}

/* Layout & Common */
.app-body {
    background-color: var(--background);
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--text-primary);
    line-height: 1.5;
}

.layout-container {
    display: flex;
    flex-direction: column;
}

@media (min-width: 768px) {
    .layout-container {
        flex-direction: row;
    }
}

/* Sidebar */
.sidebar {
    background: linear-gradient(180deg, #4f46e5 0%, #3730a3 100%);
    color: white;
    width: 100%;
    padding: 1.5rem;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

@media (min-width: 768px) {
    .sidebar {
        width: 16rem;
        min-height: 100vh;
    }
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.app-title {
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

/* Navigation Menu */
.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    font-weight: 500;
}

.menu-icon {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: block;
    color: white;
}

@media (min-width: 768px) {
    .mobile-menu-btn {
        display: none;
    }
}

.sidebar-menu {
    display: none;
}

@media (min-width: 768px) {
    .sidebar-menu {
        display: block;
    }
}

/* Responsive Sidebar */
@media (max-width: 768px) {
    .sidebar {
        padding: 1rem;
    }
    
    .sidebar-menu {
        padding-top: 1rem;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
    }
    
    .app-title {
        font-size: 1.5rem;
    }
}

/* Navigation */
.nav-active {
    background-color: rgba(79, 70, 229, 0.8);
    border-radius: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius);
    transition: var(--transition);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.nav-link:hover, .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.menu-icon {
    width: 1.5rem;
    margin-right: 1rem;
    font-size: 1.1rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.nav-link:hover .menu-icon {
    transform: scale(1.1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 1rem;
}

/* Components */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #4338ca, #4f46e5);
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(79, 70, 229, 0.3);
}

/* Button Variations */
.btn-success {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
}

.btn-success:hover {
    background: linear-gradient(45deg, #047857, #059669);
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(5, 150, 105, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger), #dc2626);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
}

.btn-danger:hover {
    background: linear-gradient(45deg, #b91c1c, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(220, 38, 38, 0.3);
}

.card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    transition: var(--transition);
    border: 1px solid var(--border);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Form Elements */
.input-group {
    margin-bottom: 1rem;
}

.input-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.input-field {
    width: 100%;
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
}

.input-field:focus {
    outline: none;
    border-color: #4338ca;
    box-shadow: 0 0 0 2px rgba(67, 56, 202, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-input, .form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1.5px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.95rem;
    transition: var(--transition);
    color: var(--text-primary);
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
}

/* Table Styles */
.table-container {
    overflow: hidden;
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.table {
    width: 100%;
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: #f8fafc;
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-align: left;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.table td {
    padding: 1rem 1.5rem;
    color: var(--text-primary);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.table tr:last-child td {
    border-bottom: none;
}

tbody tr {
    transition: all 0.2s ease;
}

tbody tr:hover td {
    background-color: #f8fafc;
}

/* Grid */
.grid {
    display: grid;
    gap: 1rem;
}

@media (min-width: 768px) {
    .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-in-stock {
    background-color: #dcfce7;
    color: #166534;
}

.status-low-stock {
    background-color: #fef3c7;
    color: #92400e;
}

.status-out-of-stock {
    background-color: #fee2e2;
    color: #b91c1c;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c7d2fe;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #818cf8;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: #4338ca;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #4338ca transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Print Styles */
@media print {
    aside, 
    button, 
    .no-print {
        display: none !important;
    }
    
    main {
        width: 100% !important;
        padding: 0 !important;
    }
    
    .bg-white {
        box-shadow: none !important;
    }
    
    body {
        background-color: white !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons button {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* Responsive page layouts */
@media (max-width: 768px) {
    .page-container {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
}

/* Page transition animations */
.page-enter {
    opacity: 0;
    transform: translateY(20px);
}

.page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-exit {
    opacity: 1;
}

.page-exit-active {
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Animation for modals */
.modal-enter {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Cards and Components */
.section-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    letter-spacing: -0.5px;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 3rem;
    height: 3px;
    background: var(--primary);
    border-radius: 3px;
}

/* Stats Cards */
.stats-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius);
    font-size: 1.5rem;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(79, 70, 229, 0.2));
    color: var(--primary);
}

.stats-info {
    flex: 1;
}

.stats-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stats-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        padding: 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .table td, .table th {
        padding: 0.75rem 1rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Chart Containers */
.chart-container {
    position: relative;
    margin-top: 1rem;
    padding: 1.5rem;
    background: var(--surface);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

/* Button Groups */
.button-group {
    display: flex;
    gap: 0.5rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mt-4 {
    margin-top: 1rem;
}

/* Notifications */
.notification {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    background: var(--surface);
    box-shadow: var(--shadow-lg);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
    
    .card {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .table-container {
        overflow-x: auto;
    }
}
