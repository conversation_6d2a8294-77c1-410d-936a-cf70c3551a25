using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace KanvasKutuphane.BLL.Services
{
    public class EmailService
    {
        private readonly string smtpServer = "smtp.yoursmtp.com";
        private readonly int smtpPort = 587;
        private readonly string smtpUser = "<EMAIL>";
        private readonly string smtpPass = "your-password";

        public async Task SendEmailAsync(string toEmail, string subject, string body)
        {
            using (var client = new SmtpClient(smtpServer, smtpPort))
            {
                client.Credentials = new NetworkCredential(smtpUser, smtpPass);
                client.EnableSsl = true;

                var mail = new MailMessage();
                mail.From = new MailAddress(smtpUser, "Kanvas Kütüphane");
                mail.To.Add(toEmail);
                mail.Subject = subject;
                mail.Body = body;
                mail.IsBodyHtml = true;

                await client.SendMailAsync(mail);
            }
        }
    }
}
