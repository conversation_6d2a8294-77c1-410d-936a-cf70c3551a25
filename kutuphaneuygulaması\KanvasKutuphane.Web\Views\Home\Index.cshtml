@{
    ViewData["Title"] = "Ana Sayfa";
}

<!-- Hero Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="text-center py-5 bg-gradient-primary rounded-3 text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-book-open me-3"></i>Kütüphane Otomasyon Sistemi
            </h1>
            <p class="lead mb-4">Kitap, üye ve ödünç işlemlerinizi kolayca yönetin</p>
            <div class="d-flex justify-content-center gap-3 flex-wrap">
                @if (User.IsInRole("Admin"))
                {
                    <a asp-controller="Book" asp-action="Create" class="btn btn-light btn-lg btn-custom">
                        <i class="fas fa-plus me-2"></i><PERSON><PERSON>
                    </a>
                    <a asp-controller="Member" asp-action="Create" class="btn btn-outline-light btn-lg btn-custom">
                        <i class="fas fa-user-plus me-2"></i>Yeni Üye Ekle
                    </a>
                }
                else
                {
                    <a asp-controller="Book" asp-action="Index" class="btn btn-light btn-lg btn-custom">
                        <i class="fas fa-book me-2"></i>Kitapları Görüntüle
                    </a>
                    <a asp-controller="Member" asp-action="Index" class="btn btn-outline-light btn-lg btn-custom">
                        <i class="fas fa-users me-2"></i>Üyeleri Görüntüle
                    </a>
                }
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="h3 mb-4 fw-bold">
            <i class="fas fa-chart-bar me-2 text-primary"></i>Sistem İstatistikleri
        </h2>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-hover h-100 bg-primary text-white">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-book fa-3x opacity-75"></i>
                </div>
                <h5 class="card-title">Toplam Kitap</h5>
                <h2 class="display-4 fw-bold mb-3">@(ViewBag.TotalBooks ?? 0)</h2>
                <a asp-controller="Book" asp-action="Index" class="btn btn-light btn-custom">
                    <i class="fas fa-eye me-1"></i>Kitapları Görüntüle
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-hover h-100 bg-success text-white">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x opacity-75"></i>
                </div>
                <h5 class="card-title">Toplam Üye</h5>
                <h2 class="display-4 fw-bold mb-3">@(ViewBag.TotalMembers ?? 0)</h2>
                <a asp-controller="Member" asp-action="Index" class="btn btn-light btn-custom">
                    <i class="fas fa-eye me-1"></i>Üyeleri Görüntüle
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-hover h-100 bg-warning text-white">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exchange-alt fa-3x opacity-75"></i>
                </div>
                <h5 class="card-title">Aktif Ödünç</h5>
                <h2 class="display-4 fw-bold mb-3">@(ViewBag.ActiveLoans ?? 0)</h2>
                <a asp-controller="Borrowings" asp-action="Index" class="btn btn-light btn-custom">
                    <i class="fas fa-list me-1"></i>Ödünç İşlemleri
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card card-hover h-100 bg-danger text-white">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x opacity-75"></i>
                </div>
                <h5 class="card-title">Gecikmiş İade</h5>
                <h2 class="display-4 fw-bold mb-3">@(ViewBag.OverdueLoans ?? 0)</h2>
                <a asp-controller="Borrowings" asp-action="Index" class="btn btn-light btn-custom">
                    <i class="fas fa-clock me-1"></i>Gecikmiş Listesi
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Access Section -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="h3 mb-4 fw-bold">
            <i class="fas fa-bolt me-2 text-warning"></i>Hızlı Erişim
        </h2>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card card-hover h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-book fa-3x text-primary"></i>
                </div>
                <h5 class="card-title fw-bold">Kitap Yönetimi</h5>
                <p class="card-text text-muted">Kitapları görüntüleyin, ekleyin, düzenleyin veya silin.</p>
                <div class="d-grid gap-2">
                    <a asp-controller="Book" asp-action="Index" class="btn btn-primary btn-custom">
                        <i class="fas fa-list me-1"></i>Kitap Listesi
                    </a>
                    @if (User.IsInRole("Admin"))
                    {
                        <a asp-controller="Book" asp-action="Create" class="btn btn-outline-primary btn-custom">
                            <i class="fas fa-plus me-1"></i>Yeni Kitap
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card card-hover h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-success"></i>
                </div>
                <h5 class="card-title fw-bold">Üye Yönetimi</h5>
                <p class="card-text text-muted">Üyeleri görüntüleyin, ekleyin, düzenleyin veya silin.</p>
                <div class="d-grid gap-2">
                    <a asp-controller="Member" asp-action="Index" class="btn btn-success btn-custom">
                        <i class="fas fa-list me-1"></i>Üye Listesi
                    </a>
                    @if (User.IsInRole("Admin"))
                    {
                        <a asp-controller="Member" asp-action="Create" class="btn btn-outline-success btn-custom">
                            <i class="fas fa-user-plus me-1"></i>Yeni Üye
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card card-hover h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exchange-alt fa-3x text-warning"></i>
                </div>
                <h5 class="card-title fw-bold">Ödünç İşlemleri</h5>
                <p class="card-text text-muted">Kitap ödünç verme ve iade alma işlemlerini yönetin.</p>
                <div class="d-grid gap-2">
                    <a asp-controller="Borrowings" asp-action="Index" class="btn btn-warning btn-custom">
                        <i class="fas fa-list me-1"></i>Ödünç Listesi
                    </a>
                    @if (User.IsInRole("Admin"))
                    {
                        <a asp-controller="Borrowings" asp-action="Create" class="btn btn-outline-warning btn-custom">
                            <i class="fas fa-plus me-1"></i>Yeni Ödünç
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
    @if (User.IsInRole("Admin"))
    {
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card card-hover h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-cog fa-3x text-secondary"></i>
                    </div>
                    <h5 class="card-title fw-bold">Yönetici Paneli</h5>
                    <p class="card-text text-muted">Sistem ayarlarını ve genel durumu yönetin.</p>
                    <div class="d-grid gap-2">
                        <a asp-controller="Admin" asp-action="Dashboard" class="btn btn-secondary btn-custom">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card card-hover h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-line fa-3x text-info"></i>
                </div>
                <h5 class="card-title fw-bold">Raporlar</h5>
                <p class="card-text text-muted">Çeşitli kütüphane raporlarını görüntüleyin.</p>
                <div class="d-grid gap-2">
                    <a asp-controller="Report" asp-action="PopularBooks" class="btn btn-info btn-custom">
                        <i class="fas fa-chart-bar me-1"></i>Popüler Kitaplar
                    </a>
                    <a asp-controller="Report" asp-action="LateReturns" class="btn btn-outline-info btn-custom">
                        <i class="fas fa-clock me-1"></i>Gecikmiş İadeler
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card card-hover h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-cog fa-3x text-dark"></i>
                </div>
                <h5 class="card-title fw-bold">Hesap Yönetimi</h5>
                <p class="card-text text-muted">Kullanıcı hesabı ayarlarınızı yönetin.</p>
                <div class="d-grid gap-2">
                    @if (User.Identity.IsAuthenticated)
                    {
                        <div class="alert alert-success text-center">
                            <i class="fas fa-user-check me-2"></i>
                            Hoş geldiniz, <strong>@User.Identity.Name</strong>!
                        </div>
                        @if (User.IsInRole("Admin"))
                        {
                            <span class="badge bg-danger mb-2">Admin Kullanıcı</span>
                        }
                    }
                    else
                    {
                        <a asp-controller="Account" asp-action="Login" class="btn btn-dark btn-custom">
                            <i class="fas fa-sign-in-alt me-1"></i>Giriş Yap
                        </a>
                        <a asp-controller="Account" asp-action="Register" class="btn btn-outline-dark btn-custom">
                            <i class="fas fa-user-plus me-1"></i>Kayıt Ol
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Recent Books Section -->
<div class="row">
    <div class="col-12">
        <div class="card card-hover">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0 fw-bold">
                        <i class="fas fa-clock me-2 text-primary"></i>Son Eklenen Kitaplar
                    </h3>
                    <a asp-controller="Book" asp-action="Index" class="btn btn-outline-primary btn-sm btn-custom">
                        <i class="fas fa-list me-1"></i>Tüm Kitapları Görüntüle
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if (ViewBag.RecentBooks != null && ViewBag.RecentBooks.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">
                                        <i class="fas fa-id-card me-1"></i>ID
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-book me-1"></i>Başlık
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-user-edit me-1"></i>Yazar
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-tags me-1"></i>Tür
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-boxes me-1"></i>Stok
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-cogs me-1"></i>İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var book in ViewBag.RecentBooks)
                                {
                                    <tr>
                                        <td class="fw-bold text-primary">@book.Id</td>
                                        <td>
                                            <strong>@book.Title</strong>
                                        </td>
                                        <td>@book.Author</td>
                                        <td>
                                            <span class="badge bg-secondary">@book.Genre</span>
                                        </td>
                                        <td>
                                            <span class="badge @(book.Stock > 0 ? "bg-success" : "bg-danger")">
                                                @book.Stock
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a asp-controller="Book" asp-action="Details" asp-route-id="@book.Id"
                                                   class="btn btn-outline-info btn-sm" title="Detayları Görüntüle">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if (User.IsInRole("Admin"))
                                                {
                                                    <a asp-controller="Book" asp-action="Edit" asp-route-id="@book.Id"
                                                       class="btn btn-outline-warning btn-sm" title="Düzenle">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-controller="Borrowings" asp-action="Create" asp-route-bookId="@book.Id"
                                                       class="btn btn-outline-success btn-sm" title="Ödünç Ver">
                                                        <i class="fas fa-hand-holding"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz kitap eklenmemiş</h5>
                        <p class="text-muted">Sisteme ilk kitabınızı eklemek için aşağıdaki butona tıklayın.</p>
                        @if (User.IsInRole("Admin"))
                        {
                            <a asp-controller="Book" asp-action="Create" class="btn btn-primary btn-lg btn-custom">
                                <i class="fas fa-plus me-2"></i>İlk Kitabı Ekle
                            </a>
                        }
                        else
                        {
                            <a asp-controller="Account" asp-action="Login" class="btn btn-primary btn-lg btn-custom">
                                <i class="fas fa-sign-in-alt me-2"></i>Kitap Eklemek İçin Giriş Yapın
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Page load animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to cards
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Add click animation to buttons
            const buttons = document.querySelectorAll('.btn-custom');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add hover effect to statistics cards
            const statCards = document.querySelectorAll('.bg-primary, .bg-success, .bg-warning, .bg-danger');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Add loading animation to table
            const table = document.querySelector('.table');
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach((row, index) => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-20px)';
                    setTimeout(() => {
                        row.style.transition = 'all 0.4s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateX(0)';
                    }, index * 50);
                });
            }

            console.log('Kütüphane Ana Sayfa yüklendi - Tüm animasyonlar aktif!');
        });

        // Ripple effect CSS is already defined in site.css
    </script>
}
