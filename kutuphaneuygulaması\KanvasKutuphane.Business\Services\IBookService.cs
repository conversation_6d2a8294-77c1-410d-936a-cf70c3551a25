using System.Collections.Generic;
using KanvasKutuphane.Entities.Models;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business
{
    public interface IBookService
    {
        Task<int> GetTotalBookCountAsync();
        Task<List<Book>> GetRecentBooksAsync(int count);
        List<Book> GetAll();
        Book GetById(int id);
        void Add(Book book);
        void Update(Book book);
        void Delete(int id);
    }
}
