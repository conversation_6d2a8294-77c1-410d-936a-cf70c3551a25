using KutuphaneOtomasyonu.DataAccess.Repositories;
using KutuphaneOtomasyonu.Entities;
using System;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.UnitOfWork
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly KutuphaneOtomasyonuDbContext _context;
        private IBookRepository? _bookRepository;
        private IMemberRepository? _memberRepository;
        private ILoanRepository? _loanRepository;

        public UnitOfWork(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IBookRepository Books => _bookRepository ??= new BookRepository(_context);
        public IMemberRepository Members => _memberRepository ??= new MemberRepository(_context);
        public ILoanRepository Loans => _loanRepository ??= new LoanRepository(_context);

        public int Complete()
        {
            return _context.SaveChanges();
        }

        public async Task<int> CompleteAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
