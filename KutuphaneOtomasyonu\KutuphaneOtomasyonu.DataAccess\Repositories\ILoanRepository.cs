using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface ILoanRepository
    {
        // Get Methods
        Loan? GetById(int id);
        Task<Loan?> GetByIdAsync(int id);
        IEnumerable<Loan> GetAll();
        Task<IEnumerable<Loan>> GetAllAsync();
        IEnumerable<Loan> Find(Expression<Func<Loan, bool>> predicate);
        Task<IEnumerable<Loan>> FindAsync(Expression<Func<Loan, bool>> predicate);
        Loan? FirstOrDefault(Expression<Func<Loan, bool>> predicate);
        Task<Loan?> FirstOrDefaultAsync(Expression<Func<Loan, bool>> predicate);

        // Add Methods
        void Add(Loan loan);
        Task AddAsync(Loan loan);
        void AddRange(IEnumerable<Loan> loans);
        Task AddRangeAsync(IEnumerable<Loan> loans);

        // Update Method
        void Update(Loan loan);

        // Remove Methods
        void Remove(Loan loan);
        void RemoveRange(IEnumerable<Loan> loans);

        // Count Methods
        int Count();
        Task<int> CountAsync();
        int Count(Expression<Func<Loan, bool>> predicate);
        Task<int> CountAsync(Expression<Func<Loan, bool>> predicate);

        // Exists Methods
        bool Exists(Expression<Func<Loan, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<Loan, bool>> predicate);

        // Specific Methods
        IEnumerable<Loan> GetAllWithMemberAndBook();
        Task<IEnumerable<Loan>> GetAllWithMemberAndBookAsync();
        IEnumerable<Loan> GetLoanHistory(int memberId);
        Task<IEnumerable<Loan>> GetLoanHistoryAsync(int memberId);
        IEnumerable<Loan> GetOverdueLoans();
        Task<IEnumerable<Loan>> GetOverdueLoansAsync();
        IEnumerable<Loan> GetActiveLoans();
        Task<IEnumerable<Loan>> GetActiveLoansAsync();
    }
}