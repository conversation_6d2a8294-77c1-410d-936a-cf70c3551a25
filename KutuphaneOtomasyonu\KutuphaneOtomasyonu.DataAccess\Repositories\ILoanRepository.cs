using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface ILoanRepository
    {
        IEnumerable<Loan> GetAll();
        Task<IEnumerable<Loan>> GetAllAsync();
        Loan GetById(int id);
        Task<Loan> GetByIdAsync(int id);
        void Add(Loan loan);
        Task AddAsync(Loan loan);
        void Update(Loan loan);
        void Delete(int id);
        IEnumerable<Loan> GetOverdueLoans();
        Task<IEnumerable<Loan>> GetOverdueLoansAsync();
        IEnumerable<Loan> GetActiveLoansByMember(int memberId);
        Task<IEnumerable<Loan>> GetActiveLoansByMemberAsync(int memberId);
        IEnumerable<Loan> GetLoansByBook(int bookId);
        Task<IEnumerable<Loan>> GetLoansByBookAsync(int bookId);
        IEnumerable<Loan> GetLoansByDateRange(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Loan>> GetLoansByDateRangeAsync(DateTime startDate, DateTime endDate);
    }
}