using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface ILoanRepository : IRepository<Loan>
    {
        IEnumerable<Loan> GetActiveLoans();
        Task<IEnumerable<Loan>> GetActiveLoansAsync();
        IEnumerable<Loan> GetAllWithDetails();
        Task<IEnumerable<Loan>> GetAllWithDetailsAsync();
        IEnumerable<Loan> GetOverdueLoans();
        Task<IEnumerable<Loan>> GetOverdueLoansAsync();
    }
}