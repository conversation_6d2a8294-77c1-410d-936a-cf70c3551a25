using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using KanvasKutuphane.Business;
using KanvasKutuphane.Business.Services; // IReportService için eklendi
using System.Threading.Tasks;

namespace KanvasKutuphane.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly IBookService _bookService;
        private readonly IMemberService _memberService;
        private readonly IBorrowingService _borrowingService;
        private readonly IReportService _reportService;

        public HomeController(IBookService bookService, IMemberService memberService, IBorrowingService borrowingService, IReportService reportService)
        {
            _bookService = bookService;
            _memberService = memberService;
            _borrowingService = borrowingService;
            _reportService = reportService;
        }

        [AllowAnonymous]
        public async Task<IActionResult> Index()
        {
            ViewBag.TotalBooks = await _bookService.GetTotalBookCountAsync();
            ViewBag.TotalMembers = await _memberService.GetTotalMemberCountAsync();
            ViewBag.ActiveLoans = await _borrowingService.GetActiveLoanCountAsync();
            ViewBag.OverdueLoans = await _borrowingService.GetOverdueLoanCountAsync();
            ViewBag.RecentBooks = await _bookService.GetRecentBooksAsync(5); // Son 5 kitabı al

            return View();
        }
    }
}
