using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using KanvasKutuphane.Business; // Added
using System.Threading.Tasks;

namespace KanvasKutuphane.Web.Controllers
{
    [Authorize(Roles = "Admin")]
    public class AdminController : Controller
    {
        private readonly IBookService _bookService;
        private readonly IMemberService _memberService;
        private readonly IBorrowingService _borrowingService;

        public AdminController(IBookService bookService, IMemberService memberService, IBorrowingService borrowingService)
        {
            _bookService = bookService;
            _memberService = memberService;
            _borrowingService = borrowingService;
        }

        public async Task<IActionResult> Dashboard()
        {
            ViewBag.TotalBooks = await _bookService.GetTotalBookCountAsync();
            ViewBag.ActiveMembers = await _memberService.GetActiveMemberCountAsync();
            ViewBag.BorrowedBooks = await _borrowingService.GetActiveLoanCountAsync(); // Metot adı düzeltildi
            ViewBag.OverdueReturns = await _borrowingService.GetOverdueLoanCountAsync(); // Metot adı düzeltildi

            return View();
        }
    }
}
