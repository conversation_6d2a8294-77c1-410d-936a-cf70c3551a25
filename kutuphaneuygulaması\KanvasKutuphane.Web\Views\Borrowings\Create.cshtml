@model KanvasKutuphane.Entities.Models.Borrowing
@{
    ViewData["Title"] = "<PERSON><PERSON> Verme";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i><PERSON><PERSON>
                </h2>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div class="mb-3">
                        <label asp-for="MemberId" class="form-label fw-bold">
                            <i class="fas fa-user me-1"></i>Üye
                        </label>
                        <select asp-for="MemberId" asp-items='new SelectList(ViewBag.Members, "Id", "FirstName")' class="form-select">
                            <option value="">-- Üye Seçin --</option>
                        </select>
                        <span asp-validation-for="MemberId" class="text-danger"></span>
                    </div>
                    <div class="mb-3">
                        <label asp-for="BookId" class="form-label fw-bold">
                            <i class="fas fa-book me-1"></i>Kitap
                        </label>
                        <select asp-for="BookId" asp-items='new SelectList(ViewBag.Books, "Id", "Title")' class="form-select">
                            <option value="">-- Kitap Seçin --</option>
                        </select>
                        <span asp-validation-for="BookId" class="text-danger"></span>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a asp-action="Index" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-1"></i>Geri
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Kaydet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
