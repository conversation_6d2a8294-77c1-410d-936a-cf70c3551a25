@model KanvasKutuphane.Entities.Models.Borrowing
@{
    ViewData["Title"] = "<PERSON><PERSON> Verme";
}

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON></h2>

<form asp-action="Create" method="post" class="max-w-lg">
    <div class="mb-4">
        <label asp-for="MemberId" class="block font-semibold">Üye</label>
        <select asp-for="MemberId" asp-items="new SelectList(ViewBag.Members, \"Id\", \"FirstName\")" class="w-full border px-3 py-2 rounded"></select>
    </div>
    <div class="mb-4">
        <label asp-for="BookId" class="block font-semibold">Kitap</label>
        <select asp-for="BookId" asp-items="new SelectList(ViewBag.Books, \"Id\", \"Title\")" class="w-full border px-3 py-2 rounded"></select>
    </div>
    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Kaydet</button>
</form>
