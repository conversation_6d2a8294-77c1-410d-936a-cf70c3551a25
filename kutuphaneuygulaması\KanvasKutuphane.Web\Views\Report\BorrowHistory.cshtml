@model List<KanvasKutuphane.Business.ViewModels.BorrowHistoryViewModel>

@{
    ViewData["Title"] = "<PERSON><PERSON> Ödünç Geçmişi";
}

<h2 class="text-2xl font-semibold mb-4"><PERSON><PERSON> Ödünç G<PERSON>çmişi</h2>

<table class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b">Kitap Adı</th>
            <th class="py-2 px-4 border-b"><PERSON><PERSON><PERSON> Tarihi</th>
            <th class="py-2 px-4 border-b"><PERSON><PERSON><PERSON></th>
            <th class="py-2 px-4 border-b"><PERSON><PERSON><PERSON>?</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="py-2 px-4 border-b">@item.BookTitle</td>
                <td class="py-2 px-4 border-b">@item.BorrowedDate.ToShortDateString()</td>
                <td class="py-2 px-4 border-b">@item.DueDate.ToShortDateString()</td>
                <td class="py-2 px-4 border-b">
                    @if (item.IsReturned)
                    {
                        <span class="text-green-600 font-semibold">Evet</span>
                    }
                    else
                    {
                        <span class="text-red-600 font-semibold">Hayır</span>
                    }
                </td>
            </tr>
        }
    </tbody>
</table>
