using Microsoft.AspNetCore.Mvc;
using KutuphaneOtomasyonu.BusinessLogic.Services;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Linq;
using KutuphaneOtomasyonu.Entities;
using System;

namespace KutuphaneOtomasyonu.Controllers
{
    [Authorize(Roles = "Admin,Kütüphaneci")]
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;
        private readonly IMemberService _memberService;
        private readonly ICategoryService _categoryService;

        public ReportController(
            IReportService reportService, 
            IMemberService memberService,
            ICategoryService categoryService)
        {
            _reportService = reportService;
            _memberService = memberService;
            _categoryService = categoryService;
        }

        public IActionResult Index()
        {
            return View();
        }

        public async Task<IActionResult> PopularBooks()
        {
            var books = await _reportService.GetMostPopularBooksAsync();
            return View(books);
        }

        public async Task<IActionResult> OverdueLoans()
        {
            var loans = await _reportService.GetOverdueLoansAsync();
            return View(loans);
        }

        public async Task<IActionResult> MemberLoanHistory(int? id)
        {
            if (id == null)
            {
                var members = await _memberService.GetAllMembersAsync();
                return View("SelectMember", members);
            }

            var member = await _memberService.GetMemberByIdAsync(id.Value);
            if (member == null)
            {
                return NotFound();
            }

            var loans = await _reportService.GetLoanHistoryByMemberAsync(id.Value);
            ViewBag.Member = member;
            return View(loans);
        }

        public async Task<IActionResult> BooksByGenre()
        {
            var statistics = await _reportService.GetBooksByGenreStatisticsAsync();
            return View(statistics);
        }

        public async Task<IActionResult> MonthlyLoanStatistics()
        {
            var statistics = await _reportService.GetMonthlyLoanStatisticsAsync();
            return View(statistics);
        }

        // Kategori İşlemleri
        public IActionResult Categories()
        {
            var categories = _categoryService.GetAllCategories();
            return View(categories);
        }

        public IActionResult CreateCategory()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult CreateCategory(Category category)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    _categoryService.AddCategory(category);
                    TempData["SuccessMessage"] = "Kategori başarıyla eklendi.";
                    return RedirectToAction(nameof(Categories));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Kategori eklenirken bir hata oluştu: " + ex.Message);
                }
            }
            return View(category);
        }

        public IActionResult EditCategory(int id)
        {
            var category = _categoryService.GetCategoryById(id);
            if (category == null)
            {
                return NotFound();
            }
            return View(category);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult EditCategory(int id, Category category)
        {
            if (id != category.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _categoryService.UpdateCategory(category);
                    TempData["SuccessMessage"] = "Kategori başarıyla güncellendi.";
                    return RedirectToAction(nameof(Categories));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Kategori güncellenirken bir hata oluştu: " + ex.Message);
                }
            }
            return View(category);
        }

        public IActionResult DeleteCategory(int id)
        {
            var category = _categoryService.GetCategoryById(id);
            if (category == null)
            {
                return NotFound();
            }
            return View(category);
        }

        [HttpPost, ActionName("DeleteCategory")]
        [ValidateAntiForgeryToken]
        public IActionResult DeleteCategoryConfirmed(int id)
        {
            try
            {
                _categoryService.DeleteCategory(id);
                TempData["SuccessMessage"] = "Kategori başarıyla silindi.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Kategori silinirken bir hata oluştu: " + ex.Message;
            }
            return RedirectToAction(nameof(Categories));
        }
    }
}
