using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using KanvasKutuphane.DataAccess;
using KanvasKutuphane.Entities.Models;
using System.Linq;
using System.Linq.Expressions; // Added for CountAsync predicate

namespace KanvasKutuphane.Business.Services
{
    public class BorrowingManager : IBorrowingService
    {
        private readonly IBorrowingRepository _borrowingRepository;

        public BorrowingManager(IBorrowingRepository borrowingRepository)
        {
            _borrowingRepository = borrowingRepository;
        }

        public List<Borrowing> GetAll()
        {
            return _borrowingRepository.GetAllAsync().Result.ToList();
        }

        public Borrowing GetById(int id)
        {
            return _borrowingRepository.GetByIdAsync(id).Result;
        }

        public void Add(Borrowing borrowing)
        {
            _borrowingRepository.AddAsync(borrowing).Wait();
        }

        public void Update(Borrowing borrowing)
        {
            _borrowingRepository.UpdateAsync(borrowing).Wait();
        }

        public void Delete(int id)
        {
            _borrowingRepository.DeleteAsync(id).Wait();
        }

        public List<Borrowing> GetActiveBorrowings()
        {
            // GetAllAsync'in predicate alan aşırı yüklemesi olmadığı için tümünü çekip filtreliyoruz.
            return _borrowingRepository.GetAllAsync().Result.Where(b => b.ReturnDate == null).ToList();
        }

        public List<Borrowing> GetOverdueBorrowings()
        {
            return _borrowingRepository.GetOverdueBorrowingsAsync().Result.ToList();
        }

        public List<Borrowing> GetBorrowingsByMemberId(int memberId)
        {
            return _borrowingRepository.GetBorrowingsByMemberIdAsync(memberId).Result.ToList();
        }

        public async Task<int> GetActiveLoanCountAsync()
        {
            return await _borrowingRepository.CountAsync(b => b.ReturnDate == null);
        }

        public async Task<int> GetOverdueLoanCountAsync()
        {
            return await _borrowingRepository.CountAsync(b => b.ReturnDate == null && b.DueDate < DateTime.Now);
        }
    }
}
