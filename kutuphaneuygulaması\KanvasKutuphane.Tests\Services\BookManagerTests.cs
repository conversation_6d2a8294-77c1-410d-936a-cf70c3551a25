using KanvasKutuphane.Business.Concrete;
using KanvasKutuphane.Entities.Models;
using KanvasKutuphane.DataAccess.Repositories;
using Moq;
using System.Collections.Generic;
using Xunit;

namespace KanvasKutuphane.Tests.Services
{
    public class BookManagerTests
    {
        [Fact]
        public void GetAll_ShouldReturnBooks()
        {
            // Arrange
            var mockRepo = new Mock<IBookRepository>();
            mockRepo.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(new List<Book> { new Book { Id = 1, Title = "Test Kitap" } });

            var manager = new BookManager(mockRepo.Object);

            // Act
            var result = manager.GetAll();

            // Assert
            Assert.NotNull(result);
        }
    }
}
