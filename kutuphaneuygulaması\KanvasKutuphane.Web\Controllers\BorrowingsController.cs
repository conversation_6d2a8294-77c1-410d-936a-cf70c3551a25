using KanvasKutuphane.Business; // Added
using KanvasKutuphane.Entities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;

namespace KanvasKutuphane.Web.Controllers
{
    public class BorrowingsController : Controller
    {
        private readonly IBorrowingService _borrowingService;
        private readonly IBookService _bookService;
        private readonly IMemberService _memberService;

        public BorrowingsController(
            IBorrowingService borrowingService,
            IBookService bookService,
            IMemberService memberService)
        {
            _borrowingService = borrowingService;
            _bookService = bookService;
            _memberService = memberService;
        }

        public IActionResult Index()
        {
            var list = _borrowingService.GetAll();
            return View(list);
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Create()
        {
            ViewBag.Books = _bookService.GetAll();
            ViewBag.Members = _memberService.GetAll();
            return View();
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public IActionResult Create(Borrowing borrowing)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.Books = _bookService.GetAll();
                ViewBag.Members = _memberService.GetAll();
                return View(borrowing);
            }

            borrowing.BorrowDate = DateTime.Now;
            borrowing.DueDate = borrowing.BorrowDate.AddDays(14); // 2 hafta
            _borrowingService.Add(borrowing);

            // stok azaltma işlemi
            var book = _bookService.GetById(borrowing.BookId);
            if (book != null && book.Stock > 0)
            {
                book.Stock -= 1;
                _bookService.Update(book);
            }

            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Edit(int id)
        {
            var borrowing = _borrowingService.GetById(id);
            if (borrowing == null) return NotFound();
            return View(borrowing);
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public IActionResult Edit(Borrowing borrowing)
        {
            if (!ModelState.IsValid) return View(borrowing);

            // iade kontrolü
            var existing = _borrowingService.GetById(borrowing.Id);
            if (existing != null && existing.ReturnDate == null && borrowing.ReturnDate != null)
            {
                // stok artırma
                var book = _bookService.GetById(existing.BookId);
                if (book != null)
                {
                    book.Stock += 1;
                    _bookService.Update(book);
                }
            }

            _borrowingService.Update(borrowing);
            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Delete(int id)
        {
            var borrowing = _borrowingService.GetById(id);
            if (borrowing == null) return NotFound();
            return View(borrowing);
        }

        [HttpPost, ActionName("Delete")]
        [Authorize(Roles = "Admin")]
        public IActionResult DeleteConfirmed(int id)
        {
            _borrowingService.Delete(id);
            return RedirectToAction(nameof(Index));
        }
    }

   }
