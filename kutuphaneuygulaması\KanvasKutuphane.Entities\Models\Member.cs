using System;
using System.ComponentModel.DataAnnotations;

namespace KanvasKutuphane.Entities.Models
{
    public class Member
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string FirstName { get; set; }

        [Required]
        [StringLength(50)]
        public string LastName { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Phone]
        public string Phone { get; set; }

        public DateTime JoinDate { get; set; }
        public bool IsActive { get; set; } // Added
    }
}
