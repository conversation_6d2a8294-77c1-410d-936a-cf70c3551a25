using KanvasKutuphane.Business; // KanvasKutuphane.Business.Services yerine
using KanvasKutuphane.Business.Services; // IReportService için
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace KanvasKutuphane.Web.Controllers
{
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;

        public ReportController(IReportService reportService)
        {
            _reportService = reportService;
        }

        public async Task<IActionResult> PopularBooks()
        {
            var result = await _reportService.GetMostBorrowedBooksAsync(10); // Varsayılan olarak 10 kitap
            return View(result);
        }

        public async Task<IActionResult> LateReturns()
        {
            var result = await _reportService.GetLateReturnsAsync();
            return View(result);
        }

        public async Task<IActionResult> MemberBorrowHistory(int memberId)
        {
            var result = await _reportService.GetBorrowingHistoryByMemberIdAsync(memberId);
            ViewBag.MemberId = memberId;
            return View(result);
        }
    }
}
