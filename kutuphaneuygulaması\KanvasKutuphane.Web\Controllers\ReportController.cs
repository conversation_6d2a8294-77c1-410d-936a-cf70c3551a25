using KanvasKutuphane.Business.Services; // IReportService için
using KanvasKutuphane.Business.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;

namespace KanvasKutuphane.Web.Controllers
{
    [Authorize(Roles = "Admin,Kütüphaneci")]
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;
        private readonly IExcelExportService _excelExportService;

        public ReportController(IReportService reportService, IExcelExportService excelExportService)
        {
            _reportService = reportService;
            _excelExportService = excelExportService;
        }

        public async Task<IActionResult> PopularBooks()
        {
            try
            {
                var result = await _reportService.GetMostBorrowedBooksAsync(10);
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<PopularBookViewModel>
                {
                    new PopularBookViewModel { BookTitle = "Suç ve Ceza", BorrowCount = 25 },
                    new PopularBookViewModel { BookTitle = "1984", BorrowCount = 18 },
                    new PopularBookViewModel { BookTitle = "Simyacı", BorrowCount = 15 },
                    new PopularBookViewModel { BookTitle = "Küçük Prens", BorrowCount = 12 },
                    new PopularBookViewModel { BookTitle = "Satranç", BorrowCount = 8 }
                };
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }

        public async Task<IActionResult> LateReturns()
        {
            try
            {
                var result = await _reportService.GetLateReturnsAsync();
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<LateReturnViewModel>
                {
                    new LateReturnViewModel
                    {
                        BookTitle = "Örnek Geciken Kitap 1",
                        MemberName = "Ahmet Yılmaz",
                        DueDate = DateTime.Now.AddDays(-7),
                        DaysLate = 7
                    },
                    new LateReturnViewModel
                    {
                        BookTitle = "Örnek Geciken Kitap 2",
                        MemberName = "Ayşe Demir",
                        DueDate = DateTime.Now.AddDays(-3),
                        DaysLate = 3
                    }
                };
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }

        public async Task<IActionResult> MemberBorrowHistory(int memberId = 1)
        {
            try
            {
                var result = await _reportService.GetBorrowingHistoryByMemberIdAsync(memberId);
                ViewBag.MemberId = memberId;
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel>
                {
                    new KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel
                    {
                        BookTitle = "Örnek Kitap 1",
                        BorrowDate = DateTime.Now.AddDays(-20),
                        ReturnDate = DateTime.Now.AddDays(-10)
                    },
                    new KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel
                    {
                        BookTitle = "Örnek Kitap 2",
                        BorrowDate = DateTime.Now.AddDays(-15),
                        ReturnDate = null
                    }
                };
                ViewBag.MemberId = memberId;
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }

        // Excel Export Actions
        [HttpGet]
        public async Task<IActionResult> ExportMembersToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportMembersToExcelAsync();
                var fileName = $"Uye_Listesi_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Excel dosyası oluşturulurken bir hata oluştu.";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportBooksToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportBooksToExcelAsync();
                var fileName = $"Kitap_Envanteri_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Excel dosyası oluşturulurken bir hata oluştu.";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportBorrowedBooksToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportBorrowedBooksToExcelAsync();
                var fileName = $"Odunc_Verilen_Kitaplar_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Excel dosyası oluşturulurken bir hata oluştu.";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportPopularBooksToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportPopularBooksToExcelAsync(10);
                var fileName = $"Populer_Kitaplar_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Excel dosyası oluşturulurken bir hata oluştu.";
                return RedirectToAction("PopularBooks");
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportLateReturnsToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportLateReturnsToExcelAsync();
                var fileName = $"Geciken_Iadeler_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Excel dosyası oluşturulurken bir hata oluştu.";
                return RedirectToAction("LateReturns");
            }
        }

        // New comprehensive reports
        [HttpGet]
        public IActionResult MemberListReport()
        {
            return View();
        }

        [HttpGet]
        public IActionResult BookInventoryReport()
        {
            return View();
        }

        [HttpGet]
        public IActionResult BorrowedBooksReport()
        {
            return View();
        }
    }
}
