using KanvasKutuphane.Business.Services; // IReportService için
using KanvasKutuphane.Business.ViewModels;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;

namespace KanvasKutuphane.Web.Controllers
{
    public class ReportController : Controller
    {
        private readonly IReportService _reportService;

        public ReportController(IReportService reportService)
        {
            _reportService = reportService;
        }

        public async Task<IActionResult> PopularBooks()
        {
            try
            {
                var result = await _reportService.GetMostBorrowedBooksAsync(10);
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<PopularBookViewModel>
                {
                    new PopularBookViewModel { BookTitle = "Suç ve Ceza", BorrowCount = 25 },
                    new PopularBookViewModel { BookTitle = "1984", BorrowCount = 18 },
                    new PopularBookViewModel { BookTitle = "Simyacı", BorrowCount = 15 },
                    new PopularBookViewModel { BookTitle = "Küçük Prens", BorrowCount = 12 },
                    new PopularBookViewModel { BookTitle = "Satranç", BorrowCount = 8 }
                };
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }

        public async Task<IActionResult> LateReturns()
        {
            try
            {
                var result = await _reportService.GetLateReturnsAsync();
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<LateReturnViewModel>
                {
                    new LateReturnViewModel
                    {
                        BookTitle = "Örnek Geciken Kitap 1",
                        MemberName = "Ahmet Yılmaz",
                        DueDate = DateTime.Now.AddDays(-7),
                        DaysLate = 7
                    },
                    new LateReturnViewModel
                    {
                        BookTitle = "Örnek Geciken Kitap 2",
                        MemberName = "Ayşe Demir",
                        DueDate = DateTime.Now.AddDays(-3),
                        DaysLate = 3
                    }
                };
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }

        public async Task<IActionResult> MemberBorrowHistory(int memberId = 1)
        {
            try
            {
                var result = await _reportService.GetBorrowingHistoryByMemberIdAsync(memberId);
                ViewBag.MemberId = memberId;
                return View(result);
            }
            catch (Exception ex)
            {
                // Hata durumunda örnek veri göster
                var sampleData = new List<KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel>
                {
                    new KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel
                    {
                        BookTitle = "Örnek Kitap 1",
                        BorrowDate = DateTime.Now.AddDays(-20),
                        ReturnDate = DateTime.Now.AddDays(-10)
                    },
                    new KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel
                    {
                        BookTitle = "Örnek Kitap 2",
                        BorrowDate = DateTime.Now.AddDays(-15),
                        ReturnDate = null
                    }
                };
                ViewBag.MemberId = memberId;
                ViewBag.ErrorMessage = "Veriler yüklenirken bir hata oluştu. Örnek veriler gösteriliyor.";
                return View(sampleData);
            }
        }
    }
}
