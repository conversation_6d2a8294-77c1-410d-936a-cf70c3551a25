using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using KutuphaneOtomasyonu.DataAccess;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class CategoryRepository : ICategoryRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public CategoryRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Category> GetAll()
        {
            return _context.Categories.Include(c => c.Books).ToList();
        }

        public async Task<IEnumerable<Category>> GetAllAsync()
        {
            return await Task.FromResult(_context.Categories.Include(c => c.Books).ToList());
        }

        public Category? GetById(int id)
        {
            return _context.Categories
                .Include(c => c.Books)
                .FirstOrDefault(c => c.Id == id);
        }

        public void Add(Category category)
        {
            _context.Categories.Add(category);
        }

        public void Update(Category category)
        {
            _context.Categories.Update(category);
        }

        public void Remove(Category category)
        {
            _context.Categories.Remove(category);
        }

        public bool Exists(string name)
        {
            return _context.Categories.Any(c => c.Name == name);
        }
    }
}
