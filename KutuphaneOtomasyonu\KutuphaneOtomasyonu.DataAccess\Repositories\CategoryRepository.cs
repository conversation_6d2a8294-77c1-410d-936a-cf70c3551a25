using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using KutuphaneOtomasyonu.DataAccess;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class CategoryRepository : ICategoryRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public CategoryRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public Category? GetById(int id)
        {
            return _context.Categories.Find(id);
        }

        public async Task<Category?> GetByIdAsync(int id)
        {
            return await _context.Categories.FindAsync(id);
        }

        public IEnumerable<Category> GetAll()
        {
            return _context.Categories.ToList();
        }

        public async Task<IEnumerable<Category>> GetAllAsync()
        {
            return await _context.Categories.ToListAsync();
        }

        public IEnumerable<Category> Find(Expression<Func<Category, bool>> predicate)
        {
            return _context.Categories.Where(predicate).ToList();
        }

        public async Task<IEnumerable<Category>> FindAsync(Expression<Func<Category, bool>> predicate)
        {
            return await _context.Categories.Where(predicate).ToListAsync();
        }

        public Category? FirstOrDefault(Expression<Func<Category, bool>> predicate)
        {
            return _context.Categories.FirstOrDefault(predicate);
        }

        public async Task<Category?> FirstOrDefaultAsync(Expression<Func<Category, bool>> predicate)
        {
            return await _context.Categories.FirstOrDefaultAsync(predicate);
        }

        public void Add(Category category)
        {
            _context.Categories.Add(category);
            _context.SaveChanges();
        }

        public async Task AddAsync(Category category)
        {
            await _context.Categories.AddAsync(category);
            await _context.SaveChangesAsync();
        }

        public void AddRange(IEnumerable<Category> categories)
        {
            _context.Categories.AddRange(categories);
            _context.SaveChanges();
        }

        public async Task AddRangeAsync(IEnumerable<Category> categories)
        {
            await _context.Categories.AddRangeAsync(categories);
            await _context.SaveChangesAsync();
        }

        public void Update(Category category)
        {
            _context.Categories.Update(category);
            _context.SaveChanges();
        }

        public void Remove(Category category)
        {
            _context.Categories.Remove(category);
            _context.SaveChanges();
        }

        public void RemoveRange(IEnumerable<Category> categories)
        {
            _context.Categories.RemoveRange(categories);
            _context.SaveChanges();
        }

        public int Count()
        {
            return _context.Categories.Count();
        }

        public async Task<int> CountAsync()
        {
            return await _context.Categories.CountAsync();
        }

        public int Count(Expression<Func<Category, bool>> predicate)
        {
            return _context.Categories.Count(predicate);
        }

        public async Task<int> CountAsync(Expression<Func<Category, bool>> predicate)
        {
            return await _context.Categories.CountAsync(predicate);
        }

        public bool Exists(Expression<Func<Category, bool>> predicate)
        {
            return _context.Categories.Any(predicate);
        }

        public async Task<bool> ExistsAsync(Expression<Func<Category, bool>> predicate)
        {
            return await _context.Categories.AnyAsync(predicate);
        }

        public IEnumerable<Category> GetAllWithBooks()
        {
            return _context.Categories.Include(c => c.Books).ToList();
        }

        public async Task<IEnumerable<Category>> GetAllWithBooksAsync()
        {
            return await _context.Categories.Include(c => c.Books).ToListAsync();
        }
    }
}
