using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class CategoryRepository : Repository<Category>, ICategoryRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public CategoryRepository(KutuphaneOtomasyonuDbContext context) : base(context)
        {
            _context = context;
        }

        public override IEnumerable<Category> GetAll()
        {
            return _context.Categories.Include(c => c.Books).ToList();
        }

        public override async Task<IEnumerable<Category>> GetAllAsync()
        {
            return await _context.Categories.Include(c => c.Books).ToListAsync();
        }

        public override Category? GetById(int id)
        {
            return _context.Categories
                .Include(c => c.Books)
                .FirstOrDefault(c => c.Id == id);
        }

        public override async Task<Category?> GetByIdAsync(int id)
        {
            return await _context.Categories
                .Include(c => c.Books)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public bool Exists(string name)
        {
            return _context.Categories.Any(c => c.Name.ToLower() == name.ToLower());
        }

        public async Task RemoveAsync(Category category)
        {
            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();
        }
    }
}
