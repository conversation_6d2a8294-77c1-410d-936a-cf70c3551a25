using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KanvasKutuphane.Entities.Models
{
    public class Borrowing
    {
        public int Id { get; set; }

        [Required]
        public int BookId { get; set; }

        [ForeignKey("BookId")]
        public Book Book { get; set; }

        [Required]
        public int MemberId { get; set; }

        [ForeignKey("MemberId")]
        public Member Member { get; set; }

        [Required]
        public DateTime BorrowDate { get; set; }

        public DateTime? DueDate { get; set; } // Nullable yapıldı

        public DateTime? ReturnDate { get; set; }
    }
}
