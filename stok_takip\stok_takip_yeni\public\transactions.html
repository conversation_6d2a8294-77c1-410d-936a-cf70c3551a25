<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stok Hareketleri</title>
   
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">

</head>
<body class="app-body">
    <div class="layout-container">
        <!-- Sidebar -->
        <aside class="sidebar"></aside>
        <!-- Main Content -->
        <main class="main-content">
            <section id="transactions" class="section">
                <div class="section-header">
                    <h1 class="section-title">Stok Hareketleri</h1>
                    <div class="button-group">
                        <button id="add-stock-btn" class="btn btn-success mr-2">
                            <i class="fas fa-plus btn-icon"></i> Stok Giriş
                        </button>
                        <button id="remove-stock-btn" class="btn btn-danger">
                            <i class="fas fa-minus btn-icon"></i> Stok Çıkış
                        </button>
                    </div>
                </div>
                
                <!-- Search and Filter -->
                <div class="card">
                    <div class="grid grid-cols-4">
                        <div class="form-group">
                            <input type="text" id="search-transaction" class="form-input" placeholder="Ara...">
                        </div>
                        <div class="form-group">
                            <select id="filter-transaction-type" class="form-select">
                                <option value="">Tüm Türler</option>
                                <option value="in">Giriş</option>
                                <option value="out">Çıkış</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="date" id="filter-transaction-start-date" class="form-input">
                        </div>
                        <div class="form-group">
                            <input type="date" id="filter-transaction-end-date" class="form-input">
                        </div>
                    </div>
                </div>
                
                <!-- Transactions Table -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                                <tr class="bg-gray-100">
                                    <th class="py-2 px-4 text-left">ID</th>
                                    <th class="py-2 px-4 text-left">Tarih</th>
                                    <th class="py-2 px-4 text-left">Ürün</th>
                                    <th class="py-2 px-4 text-left">Tür</th>
                                    <th class="py-2 px-4 text-left">Miktar</th>
                                    <th class="py-2 px-4 text-left">Açıklama</th>
                                </tr>
                            </thead>
                            <tbody id="transactions-table">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Stock Modal -->
    <div id="add-stock-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Stok Girişi</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="add-stock-form" onsubmit="handleAddStock(event)">
                    <div class="form-group">
                        <label for="stock-product" class="form-label">Ürün</label>
                        <select id="stock-product" class="form-select" required>
                            <option value="">Ürün Seçin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stock-quantity" class="form-label">Miktar</label>
                        <input type="number" id="stock-quantity" class="form-input" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="stock-note" class="form-label">Açıklama</label>
                        <textarea id="stock-note" class="form-input" rows="2"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal">İptal</button>
                        <button type="submit" class="btn btn-success">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Remove Stock Modal -->
    <div id="remove-stock-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
        <div class="modal-content bg-white w-full max-w-md mx-auto mt-20 rounded-lg shadow-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Stok Çıkışı</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="remove-stock-form" onsubmit="handleRemoveStock(event)">
                    <div class="form-group">
                        <label for="remove-stock-product" class="form-label">Ürün</label>
                        <select id="remove-stock-product" class="form-select" required>
                            <option value="">Ürün Seçin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="remove-stock-quantity" class="form-label">Miktar</label>
                        <input type="number" id="remove-stock-quantity" class="form-input" min="1" required>
                    </div>
                    <div class="form-group">
                        <label for="remove-stock-note" class="form-label">Açıklama</label>
                        <textarea id="remove-stock-note" class="form-input" rows="2"></textarea>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="btn btn-secondary close-modal">İptal</button>
                        <button type="submit" class="btn btn-danger">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Main JavaScript -->
    <script src="app.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>