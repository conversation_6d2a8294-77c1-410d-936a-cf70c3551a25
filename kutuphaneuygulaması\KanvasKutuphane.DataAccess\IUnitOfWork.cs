using System;
using System.Threading.Tasks;
// using KanvasKutuphane.DataAccess.Repositories; // Repository arayüzleri için

namespace KanvasKutuphane.DataAccess
{
    public interface IUnitOfWork : IDisposable
    {
        IBookRepository BookRepository { get; }
        IMemberRepository MemberRepository { get; }
        IBorrowingRepository BorrowingRepository { get; }
        // IUserRepository UserRepository { get; } // Eğer varsa - Kaldırıldı

        Task<int> SaveAsync();
    }
}
