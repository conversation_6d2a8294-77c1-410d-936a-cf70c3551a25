using KutuphaneOtomasyonu.DataAccess.Repositories;
using KutuphaneOtomasyonu.Entities;
using System;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.UnitOfWork
{
    public interface IUnitOfWork : IDisposable
    {
        IBookRepository Books { get; }
        IRepository<Member> Members { get; }
        IRepository<Loan> Loans { get; }

        int Complete();
        Task<int> CompleteAsync();
    }
}
