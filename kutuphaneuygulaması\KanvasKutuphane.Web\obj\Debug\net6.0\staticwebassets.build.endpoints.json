{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 13:53:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k="}]}, {"Route": "css/site.vswas0zoov.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 13:53:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vswas<PERSON><PERSON>ov"}, {"Name": "integrity", "Value": "sha256-j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "js/site.g1sio57pci.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:53:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g1sio57pci"}, {"Name": "integrity", "Value": "sha256-dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:53:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw="}]}]}