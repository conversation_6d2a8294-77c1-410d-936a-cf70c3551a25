/* <PERSON><PERSON><PERSON> - Custom Styles */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8fafc;
    line-height: 1.6;
}

/* Custom Variables */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --light-bg: #f8fafc;
    --dark-text: #1e293b;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* Enhanced <PERSON><PERSON> Styles */
.btn-custom {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-custom:active {
    transform: translateY(0);
}

/* Card Enhancements */
.card-hover {
    transition: var(--transition);
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Navigation Enhancements */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 6px;
    margin: 0 2px;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

/* Table Enhancements */
.table {
    font-size: 0.95rem;
}

.table th {
    font-weight: 600;
    border-top: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-color), 0.05);
}

/* Footer Styles */
.footer-custom {
    background-color: #1e293b;
    color: #94a3b8;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Badge Enhancements */
.badge {
    font-size: 0.8em;
    padding: 0.4em 0.6em;
}

/* Focus Styles */
button:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .card-hover {
        margin-bottom: 1rem;
    }

    .btn-custom {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Save Button Styles */
.btn-save {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border: none;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-save:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
    color: white;
}

.btn-save:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.btn-save:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.3);
}

.btn-save::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-save:hover::before {
    left: 100%;
}

/* Submit Button Variants */
.btn-submit-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border: none;
    color: white;
    font-weight: 600;
    font-size: 1.05rem;
    padding: 0.7rem 1.8rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-submit-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
    color: white;
}

.btn-submit-warning {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    border: none;
    color: white;
    font-weight: 600;
    font-size: 1.05rem;
    padding: 0.7rem 1.8rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3);
    transition: all 0.3s ease;
}

.btn-submit-warning:hover {
    background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(217, 119, 6, 0.4);
    color: white;
}

/* Pulsing Animation for Important Buttons */
.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(5, 150, 105, 0);
    }
}

/* Loading State for Buttons */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1d4ed8;
}
