using Microsoft.AspNetCore.Mvc;
using KutuphaneOtomasyonu.Entities;
using KutuphaneOtomasyonu.BusinessLogic.Services;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.Controllers
{
    [Authorize(Roles = "Admin")]
    public class BookController : Controller
    {
        private readonly IBookService _bookService;
        private readonly ICategoryService _categoryService;

        public BookController(IBookService bookService, ICategoryService categoryService)
        {
            _bookService = bookService;
            _categoryService = categoryService;
        }

        // Tüm kitapları listele
        [AllowAnonymous]
        public async Task<IActionResult> Index(string searchTerm)
        {
            IEnumerable<Book> books;

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                books = await _bookService.SearchBooksAsync(searchTerm);
                ViewBag.SearchTerm = searchTerm;
            }
            else
            {
                books = await _bookService.GetAllBooksWithCategoriesAsync();
            }

            return View(books);
        }

        // Kitap detaylarını göster
        [AllowAnonymous]
        public async Task<IActionResult> Details(int id)
        {
            var book = await _bookService.GetBookByIdAsync(id);
            if (book == null)
            {
                return NotFound();
            }
            return View(book);
        }

        // Yeni kitap ekleme formu
        public IActionResult Create()
        {
            ViewBag.Categories = _categoryService.GetAllCategories();
            return View(new Book 
            { 
                ISBN = "",
                Title = "",
                Author = "",
                Publisher = "",
                Stock = 0
            });
        }

        // Yeni kitap ekle (POST)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("ISBN,Title,Author,Publisher,CategoryId,Stock")] Book book)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _bookService.AddBookAsync(book);
                    TempData["SuccessMessage"] = $"'{book.Title}' kitabı başarıyla eklendi.";
                    return RedirectToAction(nameof(Index));
                }
                catch (InvalidOperationException ex)
                {
                    ModelState.AddModelError("", ex.Message);
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Kitap eklenirken bir hata oluştu: " + ex.Message);
                }
            }

            // If we got this far, something failed; redisplay form
            ViewBag.Categories = _categoryService.GetAllCategories();
            return View(book);
        }

        // Kitap düzenleme formu
        public async Task<IActionResult> Edit(int id)
        {
            var book = await _bookService.GetBookByIdAsync(id);
            if (book == null)
            {
                return NotFound();
            }
            ViewBag.Categories = _categoryService.GetAllCategories();
            return View(book);
        }

        // Kitap düzenle (POST)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Book book)
        {
            if (id != book.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _bookService.UpdateBookAsync(book);
                    TempData["SuccessMessage"] = $"'{book.Title}' kitabı başarıyla güncellendi.";
                    return RedirectToAction(nameof(Index));
                }
                catch (InvalidOperationException ex)
                {
                    ModelState.AddModelError("", ex.Message);
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Kitap güncellenirken bir hata oluştu: " + ex.Message);
                }
            }
            ViewBag.Categories = _categoryService.GetAllCategories();
            return View(book);
        }

        // Kitap silme onay sayfası
        public async Task<IActionResult> Delete(int id)
        {
            var book = await _bookService.GetBookByIdAsync(id);
            if (book == null)
            {
                return NotFound();
            }
            return View(book);
        }

        // Kitap sil (POST)
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _bookService.DeleteBookAsync(id);
                TempData["SuccessMessage"] = "Kitap başarıyla silindi.";
            }
            catch (InvalidOperationException ex)
            {
                TempData["ErrorMessage"] = ex.Message;
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Kitap silinirken bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }
    }
}
