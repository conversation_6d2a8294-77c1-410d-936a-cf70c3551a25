const sidebarHTML = `
    <div class="flex items-center justify-between mb-8">
        <h2 class="text-2xl font-bold">Stok Takip Menü</h2>
        <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    <nav id="sidebar-menu" class="hidden md:block">
        <ul>
            <li class="mb-2">
                <a href="dashboard.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>Ana Sayfa</span>
                </a>
            </li>
            <li class="mb-2">
                <a href="products.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-box mr-3"></i>
                    <span><PERSON><PERSON><PERSON><PERSON></span>
                </a>
            </li>
            <li class="mb-2">
                <a href="categories.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-tags mr-3"></i>
                    <span>Kategori Ekle</span>
                </a>
            </li>
            <li class="mb-2">
                <a href="transactions.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-exchange-alt mr-3"></i>
                    <span>Ürün Giriş & Çıkışı</span>
                </a>
            </li>
            <li class="mb-2">
                <a href="reports.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-chart-bar mr-3"></i>
                    <span>Raporlar</span>
                </a>
            </li>
            <li class="mb-2">
                <a href="settings.html" class="flex items-center p-2 rounded hover:bg-indigo-600 transition-colors">
                    <i class="fas fa-cog mr-3"></i>
                    <span>Ayarlar</span>
                </a>
            </li>
        </ul>
    </nav>
`;
window.addEventListener('DOMContentLoaded', function() {
    var sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.innerHTML = sidebarHTML;
        // Menüdeki mevcut sayfayı vurgula
        var links = sidebar.querySelectorAll('a');
        var current = window.location.pathname.split('/').pop();
        links.forEach(function(link) {
            if (link.getAttribute('href') === current) {
                link.classList.add('active', 'bg-indigo-600', 'text-white');
            }
        });
        // Mobil menü butonu için aç/kapa
        var mobileBtn = sidebar.querySelector('#mobile-menu-button');
        var menu = sidebar.querySelector('#sidebar-menu');
        if (mobileBtn && menu) {
            mobileBtn.addEventListener('click', function() {
                menu.classList.toggle('hidden');
            });
        }
    }
});