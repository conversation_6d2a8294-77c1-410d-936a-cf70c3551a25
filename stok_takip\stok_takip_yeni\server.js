const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const app = express();
const port = process.env.PORT || 3000;

// CORS ayarlarını güncelle
app.use(cors({
    origin: true, // İstemcinin origin'ini kabul et
    credentials: true // Kimlik bilgilerini kabul et
}));

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Statik dosyaları sun
app.use('/', express.static(path.join(__dirname, 'public')));

// Ana sayfa yönlendirmesi
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
});

// SQLite veritabanını aç veya oluştur
const db = new sqlite3.Database('./stok.db', (err) => {
    if (err) console.error(err.message);
    else console.log('Connected to SQLite database.');
});

// Tabloları oluştur
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        code TEXT,
        name TEXT,
        categoryId TEXT,
        stock INTEGER,
        unit TEXT,
        description TEXT,
        price REAL
    )`);

    db.run(`CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT
    )`);

    db.run(`CREATE TABLE IF NOT EXISTS transactions (
        id TEXT PRIMARY KEY,
        date TEXT,
        productId TEXT,
        type TEXT,
        quantity INTEGER,
        note TEXT
    )`);
});

// API endpoint örneği: Tüm ürünleri getir
app.get('/api/products', (req, res) => {
    db.all("SELECT * FROM products", [], (err, rows) => {
        if (err) return res.status(500).json({ error: err.message });
        res.json(rows);
    });
});

// Ürün ekleme endpoint'i
app.post('/api/products', (req, res) => {
    const { id, code, name, categoryId, stock, unit, description, price } = req.body;
    const sql = `INSERT INTO products (id, code, name, categoryId, stock, unit, description, price)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [id, code, name, categoryId, stock, unit, description, price];
    db.run(sql, params, function(err) {
        if (err) return res.status(500).json({ error: err.message });
        res.json({ id, message: 'Product added successfully' });
    });
});

// Ürün güncelleme endpoint'i (PUT)
app.put('/api/products/:id', (req, res) => {
    const { id } = req.params;
    const { code, name, categoryId, stock, unit, description, price } = req.body;

    // Önce ürünün var olup olmadığını kontrol et
    db.get("SELECT * FROM products WHERE id = ?", [id], (err, row) => {
        if (err) return res.status(500).json({ error: err.message });
        if (!row) return res.status(404).json({ error: 'Product not found' });

        // Ürünü güncelle
        const sql = `UPDATE products SET code = ?, name = ?, categoryId = ?, stock = ?, unit = ?, description = ?, price = ? WHERE id = ?`;
        const params = [code, name, categoryId, stock ?? row.stock, unit, description, price, id];
        db.run(sql, params, function(err) {
            if (err) return res.status(500).json({ error: err.message });
            res.json({ message: 'Product updated successfully' });
        });
    });
});

// API endpoint: Tüm kategorileri getir
app.get('/api/categories', (req, res) => {
    db.all("SELECT * FROM categories", [], (err, rows) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: err.message });
        }
        res.json(rows);
    });
});

// API endpoint: Yeni kategori ekle
app.post('/api/categories', (req, res) => {
    const { id, name, description } = req.body;
    
    // Validate input
    if (!id || !name) {
        return res.status(400).json({ error: 'ID ve isim zorunludur' });
    }

    const sql = `INSERT INTO categories (id, name, description)
                 VALUES (?, ?, ?)`;
    const params = [id, name, description || ''];
    
    db.run(sql, params, function(err) {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: err.message });
        }
        res.json({ 
            id, 
            name,
            description,
            message: 'Category added successfully' 
        });
    });
});

// API endpoint: Kategori silme
app.delete('/api/categories/:id', (req, res) => {
    const id = req.params.id;
    
    // Önce kategorinin var olup olmadığını kontrol et
    db.get("SELECT * FROM categories WHERE id = ?", [id], (err, row) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        if (!row) {
            return res.status(404).json({ error: 'Kategori bulunamadı' });
        }
        
        // Kategoriyi sil
        db.run("DELETE FROM categories WHERE id = ?", [id], (err) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            res.json({ message: 'Kategori başarıyla silindi' });
        });
    });
});

// API endpoint: Tüm hareketleri getir
app.get('/api/transactions', (req, res) => {
    db.all("SELECT * FROM transactions ORDER BY date DESC", [], (err, rows) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: err.message });
        }
        res.json(rows);
    });
});

// API endpoint: Yeni hareket ekle
app.post('/api/transactions', (req, res) => {
    const { id, date, productId, type, quantity, note } = req.body;
    
    // Validate input
    if (!id || !date || !productId || !type || !quantity) {
        return res.status(400).json({ error: 'Eksik bilgi' });
    }

    // Önce ürünün mevcut stok miktarını kontrol et
    db.get("SELECT stock FROM products WHERE id = ?", [productId], (err, product) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        
        if (!product) {
            return res.status(404).json({ error: 'Ürün bulunamadı' });
        }

        const stockChange = type === 'in' ? quantity : -quantity;
        const newStock = product.stock + stockChange;

        // Stok miktarı negatif olamaz kontrolü
        if (newStock < 0) {
            return res.status(400).json({ 
                error: 'Yetersiz stok! Bu işlem stok miktarını eksiye düşürecek.' 
            });
        }

        // Stok güncelleme ve hareket kaydı
        db.run(
            "UPDATE products SET stock = ? WHERE id = ?",
            [newStock, productId],
            function(err) {
                if (err) {
                    return res.status(500).json({ error: err.message });
                }

                const sql = `INSERT INTO transactions (id, date, productId, type, quantity, note)
                            VALUES (?, ?, ?, ?, ?, ?)`;
                const params = [id, date, productId, type, quantity, note];
                
                db.run(sql, params, function(err) {
                    if (err) {
                        return res.status(500).json({ error: err.message });
                    }
                    res.json({ 
                        message: 'Transaction added successfully',
                        id 
                    });
                });
            }
        );
    });
});

// API endpoint: Ürün silme
app.delete('/api/products/:id', (req, res) => {
    const id = req.params.id;
    
    // Önce ürünün var olup olmadığını kontrol et
    db.get("SELECT * FROM products WHERE id = ?", [id], (err, row) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        if (!row) {
            return res.status(404).json({ error: 'Ürün bulunamadı' });
        }
        
        // Ürünü sil
        db.run("DELETE FROM products WHERE id = ?", [id], (err) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            res.json({ message: 'Ürün başarıyla silindi' });
        });
    });
});

app.listen(port, () => {
    console.log(`Server running on http://localhost:${port}`);
});
