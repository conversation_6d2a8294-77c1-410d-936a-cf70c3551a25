@model List<KanvasKutuphane.Entities.ViewModels.MemberBorrowingHistoryViewModel>

@{
    ViewData["Title"] = "Üye Ödünç Geçmişi";
    var memberId = ViewBag.MemberId;
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-history me-2 text-info"></i><PERSON>ye Ödünç Geçmişi
                </h1>
                <p class="text-muted mb-0">Üye ID: @memberId - Ödünç alma geçmişi</p>
            </div>
            <div>
                <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Member Selection -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user-search me-2"></i>Üye Seç
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="MemberBorrowHistory" method="get">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="number" name="memberId" class="form-control" 
                               placeholder="Üye ID girin" value="@memberId" min="1" required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Geçmişi Görüntüle
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@if (Model != null)
{
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-book fa-2x mb-2"></i>
                    <h5 class="card-title">Toplam Ödünç</h5>
                    <h3 class="mb-0">@Model.Count</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h5 class="card-title">İade Edildi</h5>
                    <h3 class="mb-0">@Model.Count(x => x.ReturnDate.HasValue)</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h5 class="card-title">Beklemede</h5>
                    <h3 class="mb-0">@Model.Count(x => !x.ReturnDate.HasValue)</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h5 class="card-title">İade Oranı</h5>
                    <h3 class="mb-0">@(Model.Any() ? (Model.Count(x => x.ReturnDate.HasValue) * 100 / Model.Count).ToString("F0") : "0")%</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Borrow History Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Ödünç Alma Geçmişi
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th scope="col">
                                            <i class="fas fa-book me-1"></i>Kitap Adı
                                        </th>
                                        <th scope="col" class="text-center">
                                            <i class="fas fa-calendar-plus me-1"></i>Alış Tarihi
                                        </th>
                                        <th scope="col" class="text-center">
                                            <i class="fas fa-calendar-check me-1"></i>Teslim Tarihi
                                        </th>
                                        <th scope="col" class="text-center">
                                            <i class="fas fa-info-circle me-1"></i>Durum
                                        </th>
                                        <th scope="col" class="text-center">
                                            <i class="fas fa-clock me-1"></i>Süre
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.OrderByDescending(x => x.BorrowDate))
                                    {
                                        var isReturned = item.ReturnDate.HasValue;
                                        var rowClass = isReturned ? "table-success" : "";
                                        var daysDiff = isReturned ?
                                            (item.ReturnDate?.Subtract(item.BorrowDate).Days ?? 0) :
                                            DateTime.Now.Subtract(item.BorrowDate).Days;
                                        
                                        <tr class="@rowClass">
                                            <td>
                                                <strong>@item.BookTitle</strong>
                                            </td>
                                            <td class="text-center">
                                                <span class="text-muted">
                                                    @item.BorrowDate.ToString("dd.MM.yyyy")
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="text-muted">
                                                    Teslim tarihi belirtilmemiş
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                @if (isReturned)
                                                {
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>İade Edildi
                                                    </span>
                                                    @if (item.ReturnDate.HasValue)
                                                    {
                                                        <br><small class="text-muted">@item.ReturnDate.Value.ToString("dd.MM.yyyy")</small>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock me-1"></i>Beklemede
                                                    </span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-secondary">
                                                    @daysDiff gün
                                                </span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Bu üyenin ödünç geçmişi bulunmuyor</h5>
                            <p class="text-muted">Henüz hiç kitap ödünç alınmamış.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2 text-info"></i>Geçmiş Analizi
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <small><strong>En Aktif Dönem:</strong> @(Model.Any() ? Model.GroupBy(x => x.BorrowDate.Month).OrderByDescending(g => g.Count()).FirstOrDefault()?.Key.ToString() + ". Ay" : "Veri yok")</small>
                        </div>
                        <div class="col-md-4">
                            <small><strong>Ortalama Ödünç Süresi:</strong> @(Model.Where(x => x.ReturnDate.HasValue).Any() ? Model.Where(x => x.ReturnDate.HasValue).Average(x => x.ReturnDate.Value.Subtract(x.BorrowDate).Days).ToString("F0") : "0") gün</small>
                        </div>
                        <div class="col-md-4">
                            <small><strong>Son Aktivite:</strong> @(Model.Any() ? Model.Max(x => x.BorrowDate).ToString("dd.MM.yyyy") : "Veri yok")</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading animation to table rows
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.4s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            console.log('Üye ödünç geçmişi raporu yüklendi!');
        });
    </script>
}
