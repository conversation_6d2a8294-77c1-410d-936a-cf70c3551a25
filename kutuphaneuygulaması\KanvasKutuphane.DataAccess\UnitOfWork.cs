using System;
using System.Threading.Tasks;
// using KanvasKutuphane.DataAccess.Repositories; // Repository sınıfları için

namespace KanvasKutuphane.DataAccess
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly KanvasKutuphaneContext _context;
        private IBookRepository _bookRepository;
        private IMemberRepository _memberRepository;
        private IBorrowingRepository _borrowingRepository;
        // private IUserRepository _userRepository; // Eğer varsa - Kaldırıldı

        public UnitOfWork(KanvasKutuphaneContext context)
        {
            _context = context;
        }

        public IBookRepository BookRepository
        {
            get
            {
                if (_bookRepository == null)
                {
                    _bookRepository = new BookRepository(_context);
                }
                return _bookRepository;
            }
        }

        public IMemberRepository MemberRepository
        {
            get
            {
                if (_memberRepository == null)
                {
                    _memberRepository = new MemberRepository(_context);
                }
                return _memberRepository;
            }
        }

        public IBorrowingRepository BorrowingRepository
        {
            get
            {
                if (_borrowingRepository == null)
                {
                    _borrowingRepository = new BorrowingRepository(_context);
                }
                return _borrowingRepository;
            }
        }

        // public IUserRepository UserRepository // Kaldırıldı
        // {
        //     get
        //     {
        //         if (_userRepository == null)
        //         {
        //             _userRepository = new UserRepository(_context);
        //         }
        //         return _userRepository;
        //     }
        // }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
