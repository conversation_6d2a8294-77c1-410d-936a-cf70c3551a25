<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stok Hareketleri</title>

   <!-- Tailwind CSS CDN -->
   <script src="https://cdn.tailwindcss.com"></script>
   <!-- Font Awesome for icons -->
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
   <link rel="stylesheet" href="styles.css">
</head>
<body class="app-body">
    <div class="layout-container">
       <!-- Sidebar -->
       <aside class="sidebar"></aside>

        <!-- Main Content -->
        <main class="main-content">
            <section id="settings" class="section">
                <div class="section-header">
                    <h1 class="section-title">Ayarlar</h1>
                    <button id="save-settings" class="btn btn-primary">
                        <i class="fas fa-save btn-icon"></i> Değişiklikleri Kaydet
                    </button>
                </div>
                
                <!-- Settings Form -->
                <div class="card">
                    <h2 class="card-title">Sistem Ayarları</h2>
                    
                    <form id="settings-form">
                        <div class="grid grid-cols-2">
                            <div class="form-group">
                                <label class="form-label">Şirket Adı</label>
                                <input type="text" id="company-name" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Kritik Stok Eşiği</label>
                                <input type="number" id="low-stock-threshold" class="form-input" min="1" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Para Birimi</label>
                                <select id="currency" class="form-select" required>
                                    <option value="TRY">₺ - Türk Lirası</option>
                                    <option value="USD">$ - Amerikan Doları</option>
                                    <option value="EUR">€ - Euro</option>
                                    <option value="GBP">£ - İngiliz Sterlini</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Tarih Formatı</label>
                                <select id="date-format" class="form-select" required>
                                    <option value="DD/MM/YYYY">GG/AA/YYYY</option>
                                    <option value="MM/DD/YYYY">AA/GG/YYYY</option>
                                    <option value="YYYY/MM/DD">YYYY/AA/GG</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </section>
        </main>
    </div>
    
    <!-- Chart.js for reports -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Main JavaScript -->
    <script src="app.js"></script>
    <script src="sidebar.js"></script>
</body>
</html>