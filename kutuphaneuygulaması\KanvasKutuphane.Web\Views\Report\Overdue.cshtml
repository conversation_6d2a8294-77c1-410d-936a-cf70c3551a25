@model List<KanvasKutuphane.Business.ViewModels.OverdueBookViewModel>
@{
    ViewData["Title"] = "Geciken İadeler";
}

<h2 class="text-2xl font-bold mb-4">Geciken İadeler</h2>

<table class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="px-4 py-2 border-b">Kitap Adı</th>
            <th class="px-4 py-2 border-b">Üye</th>
            <th class="px-4 py-2 border-b">Son Te<PERSON><PERSON> Tarihi</th>
            <th class="px-4 py-2 border-b">Gecikme (Gün)</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="px-4 py-2 border-b">@item.BookTitle</td>
                <td class="px-4 py-2 border-b">@item.MemberName</td>
                <td class="px-4 py-2 border-b">@item.DueDate.ToShortDateString()</td>
                <td class="px-4 py-2 border-b">@item.DaysOverdue</td>
            </tr>
        }
    </tbody>
</table>
