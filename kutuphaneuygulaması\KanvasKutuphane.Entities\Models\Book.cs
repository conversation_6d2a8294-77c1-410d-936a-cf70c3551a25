using System.ComponentModel.DataAnnotations;

namespace KanvasKutuphane.Entities.Models
{
    public class Book
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        [Required]
        [StringLength(100)]
        public string Author { get; set; }

        [Required]
        [StringLength(50)]
        public string Genre { get; set; }

        [Range(0, int.MaxValue)]
        public int Stock { get; set; }
    }
}
