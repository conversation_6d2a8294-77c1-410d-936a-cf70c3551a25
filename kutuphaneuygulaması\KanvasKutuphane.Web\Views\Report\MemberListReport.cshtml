@{
    ViewData["Title"] = "Üye Listesi Raporu";
}

<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users me-2 text-primary"></i>Üye Listesi Raporu
                </h1>
                <p class="text-muted mb-0">Tüm kütüphane üyelerinin detaylı listesi</p>
            </div>
            <div>
                <a href="@Url.Action("ExportMembersToExcel")" class="btn btn-success btn-lg">
                    <i class="fas fa-file-excel me-2"></i>Excel'e Aktar
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Report Content -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>Üye Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Rapor Açıklaması:</strong> Bu rapor tüm kütüphane üyelerinin detaylı bilgilerini içerir. 
                    Excel formatında indirmek için yukarıdaki "Excel'e Aktar" butonunu kullanın.
                </div>

                <!-- Report Features -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus text-success fa-2x mb-2"></i>
                                <h6>Üye Bilgileri</h6>
                                <small class="text-muted">Ad, Soyad, İletişim</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar text-info fa-2x mb-2"></i>
                                <h6>Kayıt Tarihi</h6>
                                <small class="text-muted">Üyelik başlangıç tarihi</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                <h6>Aktif Durum</h6>
                                <small class="text-muted">Üyelik durumu</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-id-card text-warning fa-2x mb-2"></i>
                                <h6>Üye Numarası</h6>
                                <small class="text-muted">Benzersiz kimlik</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Excel Export Information -->
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-download me-2"></i>Excel Raporu İçeriği
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Raporda Yer Alan Bilgiler:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Üye Numarası</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Ad ve Soyad</li>
                                    <li><i class="fas fa-check text-success me-2"></i>E-posta Adresi</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Telefon Numarası</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Ek Bilgiler:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Kayıt Tarihi</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Üyelik Durumu</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Renkli Formatlar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Otomatik Sütun Boyutlandırma</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="@Url.Action("Index", "Home")" class="btn btn-secondary me-2">
            <i class="fas fa-home me-1"></i>Ana Sayfa
        </a>
        <a href="@Url.Action("BookInventoryReport")" class="btn btn-info me-2">
            <i class="fas fa-book me-1"></i>Kitap Envanteri
        </a>
        <a href="@Url.Action("BorrowedBooksReport")" class="btn btn-warning">
            <i class="fas fa-hand-holding me-1"></i>Ödünç Kitaplar
        </a>
    </div>
</div>

<style>
    .card:hover {
        transform: translateY(-2px);
        transition: transform 0.2s ease;
    }
    
    .btn-success:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }
</style>
