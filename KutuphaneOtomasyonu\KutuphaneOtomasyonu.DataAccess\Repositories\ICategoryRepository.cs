using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface ICategoryRepository
    {
        // Get Methods
        Category? GetById(int id);
        Task<Category?> GetByIdAsync(int id);
        IEnumerable<Category> GetAll();
        Task<IEnumerable<Category>> GetAllAsync();
        IEnumerable<Category> Find(Expression<Func<Category, bool>> predicate);
        Task<IEnumerable<Category>> FindAsync(Expression<Func<Category, bool>> predicate);
        Category? FirstOrDefault(Expression<Func<Category, bool>> predicate);
        Task<Category?> FirstOrDefaultAsync(Expression<Func<Category, bool>> predicate);

        // Add Methods
        void Add(Category category);
        Task AddAsync(Category category);
        void AddRange(IEnumerable<Category> categories);
        Task AddRangeAsync(IEnumerable<Category> categories);

        // Update Method
        void Update(Category category);

        // Remove Methods
        void Remove(Category category);
        void RemoveRange(IEnumerable<Category> categories);

        // Count Methods
        int Count();
        Task<int> CountAsync();
        int Count(Expression<Func<Category, bool>> predicate);
        Task<int> CountAsync(Expression<Func<Category, bool>> predicate);

        // Exists Methods
        bool Exists(Expression<Func<Category, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<Category, bool>> predicate);

        // Specific Methods
        IEnumerable<Category> GetAllWithBooks();
        Task<IEnumerable<Category>> GetAllWithBooksAsync();
    }
}