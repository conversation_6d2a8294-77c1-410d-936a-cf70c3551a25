using KutuphaneOtomasyonu.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface ICategoryRepository
    {
        IEnumerable<Category> GetAll();
        Task<IEnumerable<Category>> GetAllAsync();
        Category? GetById(int id);
        void Add(Category category);
        void Update(Category category);
        void Remove(Category category);
        bool Exists(string name);
    }
}