using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class LoanRepository : ILoanRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public LoanRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public Loan? GetById(int id)
        {
            return _context.Loans.Find(id);
        }

        public async Task<Loan?> GetByIdAsync(int id)
        {
            return await _context.Loans.FindAsync(id);
        }

        public IEnumerable<Loan> GetAll()
        {
            return _context.Loans.ToList();
        }

        public async Task<IEnumerable<Loan>> GetAllAsync()
        {
            return await _context.Loans.ToListAsync();
        }

        public IEnumerable<Loan> Find(Expression<Func<Loan, bool>> predicate)
        {
            return _context.Loans.Where(predicate).ToList();
        }

        public async Task<IEnumerable<Loan>> FindAsync(Expression<Func<Loan, bool>> predicate)
        {
            return await _context.Loans.Where(predicate).ToListAsync();
        }

        public Loan? FirstOrDefault(Expression<Func<Loan, bool>> predicate)
        {
            return _context.Loans.FirstOrDefault(predicate);
        }

        public async Task<Loan?> FirstOrDefaultAsync(Expression<Func<Loan, bool>> predicate)
        {
            return await _context.Loans.FirstOrDefaultAsync(predicate);
        }

        public void Add(Loan loan)
        {
            _context.Loans.Add(loan);
            _context.SaveChanges();
        }

        public async Task AddAsync(Loan loan)
        {
            await _context.Loans.AddAsync(loan);
            await _context.SaveChangesAsync();
        }

        public void AddRange(IEnumerable<Loan> loans)
        {
            _context.Loans.AddRange(loans);
            _context.SaveChanges();
        }

        public async Task AddRangeAsync(IEnumerable<Loan> loans)
        {
            await _context.Loans.AddRangeAsync(loans);
            await _context.SaveChangesAsync();
        }

        public void Update(Loan loan)
        {
            _context.Loans.Update(loan);
            _context.SaveChanges();
        }

        public void Remove(Loan loan)
        {
            _context.Loans.Remove(loan);
            _context.SaveChanges();
        }

        public void RemoveRange(IEnumerable<Loan> loans)
        {
            _context.Loans.RemoveRange(loans);
            _context.SaveChanges();
        }

        public int Count()
        {
            return _context.Loans.Count();
        }

        public async Task<int> CountAsync()
        {
            return await _context.Loans.CountAsync();
        }

        public int Count(Expression<Func<Loan, bool>> predicate)
        {
            return _context.Loans.Count(predicate);
        }

        public async Task<int> CountAsync(Expression<Func<Loan, bool>> predicate)
        {
            return await _context.Loans.CountAsync(predicate);
        }

        public bool Exists(Expression<Func<Loan, bool>> predicate)
        {
            return _context.Loans.Any(predicate);
        }

        public async Task<bool> ExistsAsync(Expression<Func<Loan, bool>> predicate)
        {
            return await _context.Loans.AnyAsync(predicate);
        }

        public IEnumerable<Loan> GetAllWithMemberAndBook()
        {
            return _context.Loans
                .Include(l => l.Member)
                .Include(l => l.Book)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetAllWithMemberAndBookAsync()
        {
            return await _context.Loans
                .Include(l => l.Member)
                .Include(l => l.Book)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetLoanHistory(int memberId)
        {
            return _context.Loans
                .Include(l => l.Book)
                .Where(l => l.MemberId == memberId)
                .OrderByDescending(l => l.LoanDate)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetLoanHistoryAsync(int memberId)
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Where(l => l.MemberId == memberId)
                .OrderByDescending(l => l.LoanDate)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetActiveLoans()
        {
            return _context.Loans
                .Include(l => l.Member)
                .Include(l => l.Book)
                .Where(l => !l.ReturnDate.HasValue)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetActiveLoansAsync()
        {
            return await _context.Loans
                .Include(l => l.Member)
                .Include(l => l.Book)
                .Where(l => !l.ReturnDate.HasValue)
                .ToListAsync();
        }
    }
}
