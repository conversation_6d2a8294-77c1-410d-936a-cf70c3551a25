using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class LoanRepository : Repository<Loan>, ILoanRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public LoanRepository(KutuphaneOtomasyonuDbContext context) : base(context)
        {
            _context = context;
        }

        public IEnumerable<Loan> GetActiveLoans()
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.ReturnDate == null)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetActiveLoansAsync()
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.ReturnDate == null)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetAllWithDetails()
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetAllWithDetailsAsync()
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetOverdueLoans()
        {
            var today = DateTime.Today;
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.DueDate < today && l.ReturnDate == null)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetOverdueLoansAsync()
        {
            var today = DateTime.Today;
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.DueDate < today && l.ReturnDate == null)
                .ToListAsync();
        }
    }
}
