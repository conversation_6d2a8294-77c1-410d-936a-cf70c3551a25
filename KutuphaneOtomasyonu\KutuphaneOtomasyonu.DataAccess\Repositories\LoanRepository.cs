using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class LoanRepository : ILoanRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public LoanRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Loan> GetAll()
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetAllAsync()
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .ToListAsync();
        }

        public Loan GetById(int id)
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .FirstOrDefault(l => l.Id == id);
        }

        public async Task<Loan> GetByIdAsync(int id)
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .FirstOrDefaultAsync(l => l.Id == id);
        }

        public void Add(Loan loan)
        {
            _context.Loans.Add(loan);
            _context.SaveChanges();
        }

        public async Task AddAsync(Loan loan)
        {
            await _context.Loans.AddAsync(loan);
            await _context.SaveChangesAsync();
        }

        public void Update(Loan loan)
        {
            _context.Loans.Update(loan);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var loan = _context.Loans.FirstOrDefault(l => l.Id == id);
            if (loan != null)
            {
                _context.Loans.Remove(loan);
                _context.SaveChanges();
            }
        }

        public IEnumerable<Loan> GetOverdueLoans()
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.DueDate < DateTime.Now && l.ReturnDate == null)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetOverdueLoansAsync()
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.DueDate < DateTime.Now && l.ReturnDate == null)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetActiveLoansByMember(int memberId)
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.MemberId == memberId && l.ReturnDate == null)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetActiveLoansByMemberAsync(int memberId)
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.MemberId == memberId && l.ReturnDate == null)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetLoansByBook(int bookId)
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.BookId == bookId)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetLoansByBookAsync(int bookId)
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.BookId == bookId)
                .ToListAsync();
        }

        public IEnumerable<Loan> GetLoansByDateRange(DateTime startDate, DateTime endDate)
        {
            return _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.LoanDate >= startDate && l.LoanDate <= endDate)
                .ToList();
        }

        public async Task<IEnumerable<Loan>> GetLoansByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Loans
                .Include(l => l.Book)
                .Include(l => l.Member)
                .Where(l => l.LoanDate >= startDate && l.LoanDate <= endDate)
                .ToListAsync();
        }
    }
}
