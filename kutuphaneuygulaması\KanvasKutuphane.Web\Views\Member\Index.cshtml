@model List<KanvasKutuphane.Entities.Models.Member>

@{
    ViewData["Title"] = "Üyeler";
}

<h2 class="text-2xl font-bold mb-4">Üyeler</h2>

<a class="bg-green-600 text-white px-4 py-2 rounded mb-4 inline-block" asp-action="Create"><PERSON><PERSON></a>

<table class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="px-4 py-2 border-b">Ad</th>
            <th class="px-4 py-2 border-b">Soyad</th>
            <th class="px-4 py-2 border-b">E-Posta</th>
            <th class="px-4 py-2 border-b">Telefon</th>
            <th class="px-4 py-2 border-b"><PERSON><PERSON><PERSON><PERSON></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td class="px-4 py-2 border-b">@item.FirstName</td>
                <td class="px-4 py-2 border-b">@item.LastName</td>
                <td class="px-4 py-2 border-b">@item.Email</td>
                <td class="px-4 py-2 border-b">@item.Phone</td>
                <td class="px-4 py-2 border-b">
                    <a asp-action="Edit" asp-route-id="@item.Id" class="text-blue-600 hover:underline mr-2">Düzenle</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="text-red-600 hover:underline">Sil</a>
                </td>
            </tr>
        }
    </tbody>
</table>
