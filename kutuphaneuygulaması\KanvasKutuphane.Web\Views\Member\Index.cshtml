@model List<KanvasKutuphane.Entities.Models.Member>

@{
    ViewData["Title"] = "Üyeler";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-users me-2 text-success"></i>Üye Yönetimi
                </h1>
                <p class="text-muted mb-0">Kütüphane üyelerini görüntüleyin ve yönetin</p>
            </div>
            @if (User.IsInRole("Admin"))
            {
                <div>
                    <a asp-action="Create" class="btn btn-success btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Yeni <PERSON><PERSON>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" id="searchInput" placeholder="Üye ara (ad, soyad, e-posta...)">
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex gap-2">
            <select class="form-select" id="statusFilter">
                <option value="">Tüm Durumlar</option>
                <option value="active">Aktif Üyeler</option>
                <option value="inactive">Pasif Üyeler</option>
            </select>
            <button class="btn btn-outline-primary" onclick="exportMembers()">
                <i class="fas fa-download me-1"></i>Dışa Aktar
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h5 class="card-title">Toplam Üye</h5>
                <h3 class="mb-0">@Model.Count</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x mb-2"></i>
                <h5 class="card-title">Aktif Üye</h5>
                <h3 class="mb-0">@Model.Count(m => m.IsActive)</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-times fa-2x mb-2"></i>
                <h5 class="card-title">Pasif Üye</h5>
                <h3 class="mb-0">@Model.Count(m => !m.IsActive)</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                <h5 class="card-title">Bu Ay Katılan</h5>
                <h3 class="mb-0">@Model.Count(m => m.JoinDate.Month == DateTime.Now.Month && m.JoinDate.Year == DateTime.Now.Year)</h3>
            </div>
        </div>
    </div>
</div>

<!-- Members Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Üye Listesi
                </h5>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0" id="membersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">
                                        <i class="fas fa-id-card me-1"></i>ID
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-user me-1"></i>Ad Soyad
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-envelope me-1"></i>E-posta
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-phone me-1"></i>Telefon
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-calendar me-1"></i>Katılım Tarihi
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-info-circle me-1"></i>Durum
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-cogs me-1"></i>İşlemler
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderByDescending(m => m.JoinDate))
                                {
                                    <tr>
                                        <td class="fw-bold text-primary">@item.Id</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    @item.FirstName.Substring(0, 1).ToUpper()@item.LastName.Substring(0, 1).ToUpper()
                                                </div>
                                                <div>
                                                    <strong>@item.FirstName @item.LastName</strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="mailto:@item.Email" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>@item.Email
                                            </a>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Phone))
                                            {
                                                <a href="tel:@item.Phone" class="text-decoration-none">
                                                    <i class="fas fa-phone me-1"></i>@item.Phone
                                                </a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted">
                                                @item.JoinDate.ToString("dd.MM.yyyy")
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Aktif
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Pasif
                                                </span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id"
                                                   class="btn btn-outline-info btn-sm" title="Detayları Görüntüle">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if (User.IsInRole("Admin"))
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.Id"
                                                       class="btn btn-outline-warning btn-sm" title="Düzenle">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-controller="Report" asp-action="MemberBorrowHistory" asp-route-memberId="@item.Id"
                                                       class="btn btn-outline-primary btn-sm" title="Ödünç Geçmişi">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id"
                                                       class="btn btn-outline-danger btn-sm" title="Sil"
                                                       onclick="return confirm('Bu üyeyi silmek istediğinizden emin misiniz?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz üye eklenmemiş</h5>
                        <p class="text-muted">Sisteme ilk üyenizi eklemek için aşağıdaki butona tıklayın.</p>
                        @if (User.IsInRole("Admin"))
                        {
                            <a asp-action="Create" class="btn btn-success btn-lg">
                                <i class="fas fa-user-plus me-2"></i>İlk Üyeyi Ekle
                            </a>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const table = document.getElementById('membersTable');

            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedStatus = statusFilter.value;

                if (table) {
                    const rows = table.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const name = row.cells[1].textContent.toLowerCase();
                        const email = row.cells[2].textContent.toLowerCase();
                        const phone = row.cells[3].textContent.toLowerCase();
                        const statusText = row.cells[5].textContent.toLowerCase();

                        const matchesSearch = name.includes(searchTerm) ||
                                            email.includes(searchTerm) ||
                                            phone.includes(searchTerm);
                        const matchesStatus = !selectedStatus ||
                                            (selectedStatus === 'active' && statusText.includes('aktif')) ||
                                            (selectedStatus === 'inactive' && statusText.includes('pasif'));

                        if (matchesSearch && matchesStatus) {
                            row.style.display = '';
                            row.classList.add('fade-in');
                        } else {
                            row.style.display = 'none';
                            row.classList.remove('fade-in');
                        }
                    });
                }
            }

            searchInput.addEventListener('input', filterTable);
            statusFilter.addEventListener('change', filterTable);

            // Add loading animation to table rows
            if (table) {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach((row, index) => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        row.style.transition = 'all 0.4s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, index * 50);
                });
            }

            console.log('Üye listesi yüklendi - Arama ve filtreleme aktif!');
        });

        function exportMembers() {
            // This would implement member export functionality
            alert('Üye listesi dışa aktarma özelliği yakında eklenecek!');
        }
    </script>

    <style>
        .avatar-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
}
