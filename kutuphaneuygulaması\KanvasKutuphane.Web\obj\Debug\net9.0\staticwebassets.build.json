{"Version": 1, "Hash": "eRx2zq0LaFvU1nfeSGzF8Rxpu2Ga1M0jPOcfovPL6pg=", "Source": "KanvasKutuphane.Web", "BasePath": "_content/KanvasKutuphane.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "KanvasKutuphane.Web\\wwwroot", "Source": "KanvasKutuphane.Web", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "Pattern": "**"}], "Assets": [{"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\2r4276r1lf-00vyxlxuto.gz", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "js/site#[.{fingerprint=00vyxlxuto}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hbp<PERSON><PERSON><PERSON><PERSON>", "Integrity": "00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\4724illt5c-992htjngp4.gz", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "css/site#[.{fingerprint=992htjngp4}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1u1pl25s0t", "Integrity": "iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\jmrykza3sq-61n19gt1b8.gz", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "992htjngp4", "Integrity": "UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "00vyxlxuto", "Integrity": "cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}], "Endpoints": [{"Route": "css/site.992htjngp4.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\4724illt5c-992htjngp4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000561482313"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.992htjngp4.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.992htjngp4.css.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\4724illt5c-992htjngp4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "label", "Value": "css/site.css.gz"}, {"Name": "integrity", "Value": "sha256-iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg="}]}, {"Route": "css/site.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\4724illt5c-992htjngp4.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000561482313"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.css.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\4724illt5c-992htjngp4.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\jmrykza3sq-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.61n19gt1b8.ico.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\jmrykza3sq-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}]}, {"Route": "favicon.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\jmrykza3sq-61n19gt1b8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\jmrykza3sq-61n19gt1b8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}]}, {"Route": "js/site.00vyxlxuto.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\2r4276r1lf-00vyxlxuto.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000394944708"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2531"}, {"Name": "ETag", "Value": "\"00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00vyxlxuto"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg="}]}, {"Route": "js/site.00vyxlxuto.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7762"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00vyxlxuto"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg="}]}, {"Route": "js/site.00vyxlxuto.js.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\2r4276r1lf-00vyxlxuto.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00vyxlxuto"}, {"Name": "label", "Value": "js/site.js.gz"}, {"Name": "integrity", "Value": "sha256-00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE="}]}, {"Route": "js/site.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\2r4276r1lf-00vyxlxuto.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000394944708"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2531"}, {"Name": "ETag", "Value": "\"00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg="}]}, {"Route": "js/site.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7762"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cJa/qWDPZig3lPkMvaA8dlVZEXjYjugOySKgIXyfNVg="}]}, {"Route": "js/site.js.gz", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\obj\\Debug\\net9.0\\compressed\\2r4276r1lf-00vyxlxuto.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 10:51:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-00yM2nQSnu2X33UtnRXCuwAYmnIw9vi1yYbKafNh9uE="}]}]}