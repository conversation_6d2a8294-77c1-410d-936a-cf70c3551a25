@{
    ViewData["Title"] = "Yönetim Paneli";
}

<!-- <PERSON>er -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>Yönetim Paneli
                </h1>
                <p class="text-muted mb-0">Kütüphane sisteminin genel durumu ve yönetim araçları</p>
            </div>
            <div>
                <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-book fa-3x mb-3"></i>
                <h5 class="card-title">Toplam Kitap</h5>
                <h2 class="mb-0 display-6">@(ViewBag.TotalBooks ?? 0)</h2>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h5 class="card-title">Aktif Üye</h5>
                <h2 class="mb-0 display-6">@(ViewBag.ActiveMembers ?? 0)</h2>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                <h5 class="card-title">Ödünçteki Kitap</h5>
                <h2 class="mb-0 display-6">@(ViewBag.BorrowedBooks ?? 0)</h2>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h5 class="card-title">Geciken İade</h5>
                <h2 class="mb-0 display-6">@(ViewBag.OverdueReturns ?? 0)</h2>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Hızlı İşlemler
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Book" asp-action="Create" class="btn btn-primary btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-plus fa-2x mb-2"></i>
                            <span>Yeni Kitap Ekle</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Member" asp-action="Create" class="btn btn-success btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>Yeni Üye Ekle</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Borrowings" asp-action="Create" class="btn btn-warning btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-hand-holding fa-2x mb-2"></i>
                            <span>Ödünç Ver</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Borrowings" asp-action="Index" class="btn btn-info btn-lg w-100 h-100 d-flex flex-column justify-content-center">
                            <i class="fas fa-list fa-2x mb-2"></i>
                            <span>Ödünç Listesi</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Raporlar ve Analizler
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-primary h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-trophy fa-3x text-primary mb-3"></i>
                                <h6 class="card-title">En Popüler Kitaplar</h6>
                                <p class="card-text small text-muted">En çok ödünç alınan kitapları görüntüleyin</p>
                                <a asp-controller="Report" asp-action="PopularBooks" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>Raporu Görüntüle
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-danger h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x text-danger mb-3"></i>
                                <h6 class="card-title">Geciken İadeler</h6>
                                <p class="card-text small text-muted">Teslim tarihi geçmiş kitapları görüntüleyin</p>
                                <a asp-controller="Report" asp-action="LateReturns" class="btn btn-danger btn-sm">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Raporu Görüntüle
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-success h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-history fa-3x text-success mb-3"></i>
                                <h6 class="card-title">Üye Ödünç Geçmişi</h6>
                                <p class="card-text small text-muted">Üyelerin ödünç alma geçmişini inceleyin</p>
                                <div class="input-group input-group-sm mb-2">
                                    <input type="number" class="form-control" id="memberIdInput" placeholder="Üye ID" min="1">
                                    <button class="btn btn-success" type="button" onclick="viewMemberHistory()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Management Tools -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Yönetim Araçları
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Book" asp-action="Index" class="btn btn-outline-primary w-100">
                            <i class="fas fa-book me-2"></i>Kitap Yönetimi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Member" asp-action="Index" class="btn btn-outline-success w-100">
                            <i class="fas fa-users me-2"></i>Üye Yönetimi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a asp-controller="Borrowings" asp-action="Index" class="btn btn-outline-warning w-100">
                            <i class="fas fa-exchange-alt me-2"></i>Ödünç Yönetimi
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Veri Dışa Aktar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewMemberHistory() {
            const memberId = document.getElementById('memberIdInput').value;
            if (memberId && memberId > 0) {
                window.location.href = '@Url.Action("MemberBorrowHistory", "Report")?memberId=' + memberId;
            } else {
                alert('Lütfen geçerli bir üye ID girin.');
            }
        }

        function exportData() {
            alert('Veri dışa aktarma özelliği yakında eklenecek!');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add loading animation to cards
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.4s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            console.log('Admin Dashboard yüklendi!');
        });
    </script>

    <style>
        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-lg {
            min-height: 80px;
        }
    </style>
}
