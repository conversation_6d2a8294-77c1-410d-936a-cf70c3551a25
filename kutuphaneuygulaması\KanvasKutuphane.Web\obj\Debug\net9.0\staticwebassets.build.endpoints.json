{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.992htjngp4.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000561482313"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "ETag", "Value": "W/\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.992htjngp4.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.992htjngp4.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "992htjngp4"}, {"Name": "integrity", "Value": "sha256-iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg="}, {"Name": "label", "Value": "css/site.css.gz"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000561482313"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "ETag", "Value": "W/\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UwErzRBn2XuhjejkF0yzpzn15WjkaqsrTL7teOynO88="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1780"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:41:25 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iaLfxoWkS/JWfounqk7yCMFtfzGV7NzvdHdnS9v2nZg="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.61n19gt1b8.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000437828371"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY=\""}, {"Name": "ETag", "Value": "W/\"696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6914"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0="}]}, {"Route": "js/site.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY="}]}, {"Route": "js/site.rg59b4a5mk.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000437828371"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY=\""}, {"Name": "ETag", "Value": "W/\"696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rg59b4a5mk"}, {"Name": "integrity", "Value": "sha256-696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.rg59b4a5mk.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6914"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rg59b4a5mk"}, {"Name": "integrity", "Value": "sha256-696OLw0BeCUIyjwFtbmcqk+1EWoAN2YkaxBkzXdDEa0="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.rg59b4a5mk.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2283"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 09:43:41 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rg59b4a5mk"}, {"Name": "integrity", "Value": "sha256-F/zuXSp82EorwqHoloosVg2rKCYQWtsLpLCMyDJzEBY="}, {"Name": "label", "Value": "js/site.js.gz"}]}]}