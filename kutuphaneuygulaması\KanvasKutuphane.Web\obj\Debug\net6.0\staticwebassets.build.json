{"Version": 1, "Hash": "Zmj2W1PYTGcsmnumHBWwiPeMZamzdOsas9YFwXjpJJA=", "Source": "KanvasKutuphane.Web", "BasePath": "_content/KanvasKutuphane.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "KanvasKutuphane.Web\\wwwroot", "Source": "KanvasKutuphane.Web", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "Pattern": "**"}], "Assets": [{"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vswas<PERSON><PERSON>ov", "Integrity": "j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "SourceId": "KanvasKutuphane.Web", "SourceType": "Discovered", "ContentRoot": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\", "BasePath": "_content/KanvasKutuphane.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g1sio57pci", "Integrity": "dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 13:53:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k="}]}, {"Route": "css/site.vswas0zoov.css", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "250"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 13:53:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vswas<PERSON><PERSON>ov"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-j5Be22lQ7Rw9EB+ibfXZNmpID0wrduBGPag3+K0KI4k="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 12 May 2025 09:27:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "js/site.g1sio57pci.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:53:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g1sio57pci"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw="}]}, {"Route": "js/site.js", "AssetFile": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 13:53:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dFoLNupTO8QNTjO+HJ8DZyv0gvvtiShg9aIx42/FiQw="}]}]}