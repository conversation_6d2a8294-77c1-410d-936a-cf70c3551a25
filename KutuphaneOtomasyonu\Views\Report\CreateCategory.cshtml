@model KutuphaneOtomasyonu.Entities.Category
@{
    ViewData["Title"] = "Yeni <PERSON>";
}
<div class="card">
    <div class="card-header bg-primary text-white">
        <h4 class="mb-0"><PERSON><PERSON></h4>
    </div>
    <div class="card-body">
        <form asp-action="CreateCategory" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="mb-3">
                <label asp-for="Name" class="form-label">Kategor<PERSON> Adı</label>
                <input asp-for="Name" class="form-control" required />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="d-flex justify-content-between mt-4">
                <a asp-action="Categories" class="btn btn-secondary">Listeye Dön</a>
                <button type="submit" class="btn btn-primary">Kaydet</button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}