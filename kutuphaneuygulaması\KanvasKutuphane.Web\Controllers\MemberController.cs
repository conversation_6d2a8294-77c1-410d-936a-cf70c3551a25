using KanvasKutuphane.Business; // Added
using KanvasKutuphane.Entities.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace KanvasKutuphane.Web.Controllers
{
    public class MemberController : Controller
    {
        private readonly IMemberService _memberService;

        public MemberController(IMemberService memberService)
        {
            _memberService = memberService;
        }

        public IActionResult Index()
        {
            var members = _memberService.GetAll();
            return View(members);
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public IActionResult Create(Member member)
        {
            if (!ModelState.IsValid)
                return View(member);

            _memberService.Add(member);
            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Edit(int id)
        {
            var member = _memberService.GetById(id);
            if (member == null) return NotFound();
            return View(member);
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public IActionResult Edit(Member member)
        {
            if (!ModelState.IsValid)
                return View(member);

            _memberService.Update(member);
            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public IActionResult Delete(int id)
        {
            var member = _memberService.GetById(id);
            if (member == null) return NotFound();
            return View(member);
        }

        [HttpPost, ActionName("Delete")]
        [Authorize(Roles = "Admin")]
        public IActionResult DeleteConfirmed(int id)
        {
            _memberService.Delete(id);
            return RedirectToAction(nameof(Index));
        }
    }
}
