@model KanvasKutuphane.Entities.Models.Book
@{
    ViewData["Title"] = "Kitap Detayları";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title mb-0">
                    <i class="fas fa-book me-2"></i>Kitap Detayları
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">
                                <i class="fas fa-id-card me-1"></i>ID:
                            </dt>
                            <dd class="col-sm-8"><strong>@Model.Id</strong></dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-book me-1"></i>Başlık:
                            </dt>
                            <dd class="col-sm-8"><strong>@Model.Title</strong></dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-user-edit me-1"></i>Yazar:
                            </dt>
                            <dd class="col-sm-8">@Model.Author</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">
                                <i class="fas fa-tags me-1"></i>Tür:
                            </dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-secondary">@Model.Genre</span>
                            </dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-boxes me-1"></i>Stok:
                            </dt>
                            <dd class="col-sm-8">
                                <span class="badge @(Model.Stock > 0 ? "bg-success" : "bg-danger")">
                                    @Model.Stock adet
                                </span>
                            </dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-info-circle me-1"></i>Durum:
                            </dt>
                            <dd class="col-sm-8">
                                @if (Model.Stock > 0)
                                {
                                    <span class="badge bg-success">Mevcut</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Stokta Yok</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="d-flex gap-2 flex-wrap">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Geri
                        </a>
                        
                        @if (User.IsInRole("Admin"))
                        {
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Düzenle
                            </a>
                            
                            if (Model.Stock > 0)
                            {
                                <a asp-controller="Borrowings" asp-action="Create" asp-route-bookId="@Model.Id" class="btn btn-success">
                                    <i class="fas fa-hand-holding me-1"></i>Ödünç Ver
                                </a>
                            }
                            
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Sil
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>İstatistikler
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-book fa-3x text-primary"></i>
                    </div>
                    <h6>Bu Kitap Hakkında</h6>
                    <p class="text-muted small">
                        Bu kitabın detaylı bilgilerini yukarıda görebilirsiniz. 
                        @if (Model.Stock > 0)
                        {
                            <span class="text-success">Kitap şu anda mevcut ve ödünç alınabilir.</span>
                        }
                        else
                        {
                            <span class="text-danger">Kitap şu anda stokta bulunmuyor.</span>
                        }
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
