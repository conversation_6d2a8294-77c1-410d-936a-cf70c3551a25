using System.Collections.Generic;
using System.Threading.Tasks;
using KanvasKutuphane.DataAccess;
using KanvasKutuphane.Entities.Models;
using System.Linq;
using System.Linq.Expressions; // Added for CountAsync predicate

namespace KanvasKutuphane.Business.Services
{
    public class MemberManager : IMemberService
    {
        private readonly IMemberRepository _memberRepository;

        public MemberManager(IMemberRepository memberRepository)
        {
            _memberRepository = memberRepository;
        }

        public List<Member> GetAll()
        {
            return _memberRepository.GetAllAsync().Result.ToList();
        }

        public Member GetById(int id)
        {
            return _memberRepository.GetByIdAsync(id).Result;
        }

        public void Add(Member member)
        {
            _memberRepository.AddAsync(member).Wait();
        }

        public void Update(Member member)
        {
            _memberRepository.UpdateAsync(member).Wait();
        }

        public void Delete(int id)
        {
            _memberRepository.DeleteAsync(id).Wait();
        }

        public async Task<int> GetTotalMemberCountAsync()
        {
            return await _memberRepository.CountAsync(m => true); // Tüm üyeleri saymak için predicate eklendi
        }

        public async Task<int> GetActiveMemberCountAsync()
        {
            return await _memberRepository.CountAsync(m => m.IsActive);
        }
    }
}
