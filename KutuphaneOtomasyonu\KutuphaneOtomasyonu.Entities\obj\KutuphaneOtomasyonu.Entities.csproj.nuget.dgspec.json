{"format": 1, "restore": {"d:\\dnm\\KutuphaneOtomasyonu\\KutuphaneOtomasyonu.Entities\\KutuphaneOtomasyonu.Entities.csproj": {}}, "projects": {"d:\\dnm\\KutuphaneOtomasyonu\\KutuphaneOtomasyonu.Entities\\KutuphaneOtomasyonu.Entities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\dnm\\KutuphaneOtom<PERSON>yonu\\KutuphaneOtomasyonu.Entities\\KutuphaneOtomasyonu.Entities.csproj", "projectName": "KutuphaneOtomasyonu.Entities", "projectPath": "d:\\dnm\\KutuphaneOtom<PERSON>yonu\\KutuphaneOtomasyonu.Entities\\KutuphaneOtomasyonu.Entities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\dnm\\KutuphaneOtomasyonu\\KutuphaneOtomasyonu.Entities\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}}}}