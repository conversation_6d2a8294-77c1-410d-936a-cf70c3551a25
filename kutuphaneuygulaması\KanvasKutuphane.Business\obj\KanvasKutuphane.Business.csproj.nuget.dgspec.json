{"format": 1, "restore": {"D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Business\\KanvasKutuphane.Business.csproj": {}}, "projects": {"D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Business\\KanvasKutuphane.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Business\\KanvasKutuphane.Business.csproj", "projectName": "KanvasKutuphane.Business", "projectPath": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Business\\KanvasKutuphane.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Business\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.DataAccess\\KanvasKutuphane.DataAccess.csproj": {"projectPath": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.DataAccess\\KanvasKutuphane.DataAccess.csproj"}, "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj": {"projectPath": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"EPPlus": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}}, "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.DataAccess\\KanvasKutuphane.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.DataAccess\\KanvasKutuphane.DataAccess.csproj", "projectName": "KanvasKutuphane.DataAccess", "projectPath": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.DataAccess\\KanvasKutuphane.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.DataAccess\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj": {"projectPath": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}}, "D:\\dnm\\kutuphaneuygulaması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj", "projectName": "KanvasKutuphane.Entities", "projectPath": "D:\\dnm\\kutuphaneuy<PERSON>laması\\KanvasKutuphane.Entities\\KanvasKutuphane.Entities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\dnm\\kutuphaneuygu<PERSON>ası\\KanvasKutuphane.Entities\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.202/PortableRuntimeIdentifierGraph.json"}}}}}