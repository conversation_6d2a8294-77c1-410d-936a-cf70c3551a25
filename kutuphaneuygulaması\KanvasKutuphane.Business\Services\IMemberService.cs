using System.Collections.Generic;
using KanvasKutuphane.Entities.Models;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business
{
    public interface IMemberService
    {
        Task<int> GetTotalMemberCountAsync();
        Task<int> GetActiveMemberCountAsync();
        List<Member> GetAll();
        Member GetById(int id);
        void Add(Member member);
        void Update(Member member);
        void Delete(int id);
    }
}
