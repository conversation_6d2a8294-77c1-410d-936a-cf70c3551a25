using System.Collections.Generic;
using KanvasKutuphane.Entities.Models;
using System.Threading.Tasks;

namespace KanvasKutuphane.Business
{
    
    public interface IBorrowingService
    {
        Task<int> GetActiveLoanCountAsync();
        Task<int> GetOverdueLoanCountAsync();
        List<Borrowing> GetAll();
        Borrowing GetById(int id);
        void Add(Borrowing borrowing);
        void Update(Borrowing borrowing);
        void Delete(int id);

        List<Borrowing> GetActiveBorrowings();
        List<Borrowing> GetOverdueBorrowings();
        List<Borrowing> GetBorrowingsByMemberId(int memberId);
    }
}
