using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface IBookRepository
    {
        // Get Methods
        IEnumerable<Book> GetAllWithCategory();
        Task<IEnumerable<Book>> GetAllWithCategoryAsync();
        Book? GetById(int id);
        Task<Book?> GetByIdAsync(int id);
        IEnumerable<Book> GetAll();
        Task<IEnumerable<Book>> GetAllAsync();
        IEnumerable<Book> Find(Expression<Func<Book, bool>> predicate);
        Task<IEnumerable<Book>> FindAsync(Expression<Func<Book, bool>> predicate);
        Book? FirstOrDefault(Expression<Func<Book, bool>> predicate);
        Task<Book?> FirstOrDefaultAsync(Expression<Func<Book, bool>> predicate);

        // Add Methods
        void Add(Book book);
        Task AddAsync(Book book);
        void AddRange(IEnumerable<Book> books);
        Task AddRangeAsync(IEnumerable<Book> books);

        // Update Method
        void Update(Book book);

        // Remove Methods
        void Remove(Book book);
        void RemoveRange(IEnumerable<Book> books);

        // Count Methods
        int Count();
        Task<int> CountAsync();
        int Count(Expression<Func<Book, bool>> predicate);
        Task<int> CountAsync(Expression<Func<Book, bool>> predicate);

        // Exists Methods
        bool Exists(Expression<Func<Book, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<Book, bool>> predicate);
    }
}
