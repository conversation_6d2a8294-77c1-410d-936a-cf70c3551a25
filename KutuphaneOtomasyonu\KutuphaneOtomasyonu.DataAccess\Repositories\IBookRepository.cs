using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface IBookRepository
    {
        IEnumerable<Book> GetAllWithCategory();
        Book? GetById(int id);
        IEnumerable<Book> GetAll();
        void Add(Book book);
        void Update(Book book);
        void Remove(Book book);
        bool Exists(Expression<Func<Book, bool>> predicate);
    }
}
