using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface IBookRepository : IRepository<Book>
    {
        IEnumerable<Book> GetAllWithCategory();
        Task<IEnumerable<Book>> GetAllWithCategoryAsync();
        Task UpdateAsync(Book book);
    }
}
