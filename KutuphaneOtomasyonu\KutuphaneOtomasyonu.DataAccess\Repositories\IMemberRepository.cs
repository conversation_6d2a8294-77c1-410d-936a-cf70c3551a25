using KutuphaneOtomasyonu.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface IMemberRepository
    {
        IEnumerable<Member> GetAll();
        Task<IEnumerable<Member>> GetAllAsync();
        Member GetById(int id);
        Task<Member> GetByIdAsync(int id);
        void Add(Member member);
        Task AddAsync(Member member);
        void Update(Member member);
        void Delete(int id);
        Member GetByContactInfo(string contactInfo);
        Task<Member> GetByContactInfoAsync(string contactInfo);
    }
}