using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public interface IMemberRepository
    {
        // Get Methods
        Member? GetById(int id);
        Task<Member?> GetByIdAsync(int id);
        IEnumerable<Member> GetAll();
        Task<IEnumerable<Member>> GetAllAsync();
        IEnumerable<Member> Find(Expression<Func<Member, bool>> predicate);
        Task<IEnumerable<Member>> FindAsync(Expression<Func<Member, bool>> predicate);
        Member? FirstOrDefault(Expression<Func<Member, bool>> predicate);
        Task<Member?> FirstOrDefaultAsync(Expression<Func<Member, bool>> predicate);

        // Add Methods
        void Add(Member member);
        Task AddAsync(Member member);
        void AddRange(IEnumerable<Member> members);
        Task AddRangeAsync(IEnumerable<Member> members);

        // Update Method
        void Update(Member member);

        // Remove Methods
        void Remove(Member member);
        void RemoveRange(IEnumerable<Member> members);

        // Count Methods
        int Count();
        Task<int> CountAsync();
        int Count(Expression<Func<Member, bool>> predicate);
        Task<int> CountAsync(Expression<Func<Member, bool>> predicate);

        // Exists Methods
        bool Exists(Expression<Func<Member, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<Member, bool>> predicate);

        // Specific Methods
        Member? GetByTCKN(string tckn);
        Task<Member?> GetByTCKNAsync(string tckn);
        Member? GetByEmail(string email);
        Task<Member?> GetByEmailAsync(string email);
        IEnumerable<Member> GetAllWithLoans();
        Task<IEnumerable<Member>> GetAllWithLoansAsync();
    }
}