namespace KanvasKutuphane.Business.ViewModels
{
    public class PopularBookViewModel
    {
        public string BookTitle { get; set; } = string.Empty;
        public int BorrowCount { get; set; }

        // Calculated properties for display
        public string BorrowCountText => BorrowCount == 1 ? "1 kez" : $"{BorrowCount} kez";

        // Popularity level
        public string PopularityLevel => BorrowCount switch
        {
            >= 20 => "Çok Popüler",
            >= 10 => "Popüler",
            >= 5 => "Orta",
            _ => "Az"
        };

        public string PopularityClass => BorrowCount switch
        {
            >= 20 => "success",
            >= 10 => "primary",
            >= 5 => "warning",
            _ => "secondary"
        };
    }
}