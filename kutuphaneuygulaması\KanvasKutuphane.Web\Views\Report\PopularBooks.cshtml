@model List<KanvasKutuphane.Business.ViewModels.PopularBookViewModel> // Changed from KanvasKutuphane.BLL.ViewModels

@{
    ViewData["Title"] = "En Çok Ödünç Alınan Kitaplar";
}

<h2 class="text-2xl font-semibold mb-4">En Çok Ödünç Alınan Kitaplar</h2>

<table class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b">Kitap Adı</th>
            <th class="py-2 px-4 border-b">Ödünç Alınma Sayısı</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var book in Model)
        {
            <tr>
                <td class="py-2 px-4 border-b">@book.BookTitle</td>
                <td class="py-2 px-4 border-b">@book.BorrowCount</td>
            </tr>
        }
    </tbody>
</table>
