@model List<KanvasKutuphane.Business.ViewModels.PopularBookViewModel>

@{
    ViewData["Title"] = "En Çok Ödünç Alınan Kitaplar";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>En Çok Ödünç Alınan Kitaplar
                </h1>
                <p class="text-muted mb-0">Kütüphanede en popüler kitapların listesi</p>
            </div>
            <div>
                <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Ana Sayfa
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h5 class="card-title">Toplam Kitap</h5>
                <h3 class="mb-0">@Model.Count</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-star fa-2x mb-2"></i>
                <h5 class="card-title">En Popüler</h5>
                <h3 class="mb-0">@(Model.FirstOrDefault()?.BookTitle ?? "N/A")</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-hand-holding fa-2x mb-2"></i>
                <h5 class="card-title">Toplam Ödünç</h5>
                <h3 class="mb-0">@Model.Sum(b => b.BorrowCount)</h3>
            </div>
        </div>
    </div>
</div>

<!-- Popular Books Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Popülerlik Sıralaması
                </h5>
            </div>
            <div class="card-body p-0">
                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-medal me-1"></i>Sıra
                                    </th>
                                    <th scope="col">
                                        <i class="fas fa-book me-1"></i>Kitap Adı
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-hand-holding me-1"></i>Ödünç Sayısı
                                    </th>
                                    <th scope="col" class="text-center">
                                        <i class="fas fa-percentage me-1"></i>Popülerlik
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @{
                                    var totalBorrows = Model.Sum(b => b.BorrowCount);
                                    var rank = 1;
                                }
                                @foreach (var book in Model)
                                {
                                    var percentage = totalBorrows > 0 ? (book.BorrowCount * 100.0 / totalBorrows) : 0;
                                    var medalClass = rank == 1 ? "text-warning" : rank == 2 ? "text-secondary" : rank == 3 ? "text-warning" : "text-muted";
                                    var medalIcon = rank == 1 ? "fa-trophy" : rank == 2 ? "fa-medal" : rank == 3 ? "fa-award" : "fa-circle";

                                    <tr>
                                        <td class="text-center">
                                            <span class="@medalClass">
                                                <i class="fas @medalIcon fa-lg"></i>
                                                <strong>@rank</strong>
                                            </span>
                                        </td>
                                        <td>
                                            <strong>@book.BookTitle</strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary fs-6">
                                                @book.BorrowCount kez
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="progress me-2" style="width: 100px; height: 20px;">
                                                    <div class="progress-bar bg-success"
                                                         role="progressbar"
                                                         style="width: @percentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture)%"
                                                         aria-valuenow="@percentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture)"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <small class="text-muted">@percentage.ToString("F1")%</small>
                                            </div>
                                        </td>
                                    </tr>
                                    rank++;
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz ödünç alınmış kitap bulunmuyor</h5>
                        <p class="text-muted">Kitaplar ödünç alındıkça bu rapor güncellenecektir.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Additional Info -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>Rapor Hakkında
                </h6>
                <p class="mb-0 small">
                    Bu rapor, kütüphanedeki kitapların ödünç alınma sıklığına göre sıralanmış listesini gösterir.
                    En çok ödünç alınan kitaplar en üstte yer alır ve popülerlik yüzdesi hesaplanır.
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading animation to table rows
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.4s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 100);
            });

            console.log('Popüler kitaplar raporu yüklendi!');
        });
    </script>
}
