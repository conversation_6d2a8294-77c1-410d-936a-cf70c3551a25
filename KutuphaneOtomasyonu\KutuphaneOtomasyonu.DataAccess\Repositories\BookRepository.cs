using KutuphaneOtomasyonu.Entities;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class BookRepository : IBookRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public BookRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Book> GetAll()
        {
            return _context.Books.ToList();
        }

        public Book GetById(int id)
        {
            return _context.Books.FirstOrDefault(b => b.Id == id);
        }

        public void Add(Book book)
        {
            _context.Books.Add(book);
            _context.SaveChanges();
        }

        public void Update(Book book)
        {
            _context.Books.Update(book);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var book = _context.Books.FirstOrDefault(b => b.Id == id);
            if (book != null)
            {
                _context.Books.Remove(book);
                _context.SaveChanges();
            }
        }

        public IEnumerable<Book> GetAllWithCategory()
        {
            return _context.Books.Include(b => b.Category).ToList();
        }
    }
}
