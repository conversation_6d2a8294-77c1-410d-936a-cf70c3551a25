using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class BookRepository : Repository<Book>, IBookRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public BookRepository(KutuphaneOtomasyonuDbContext context) : base(context)
        {
            _context = context;
        }

        public IEnumerable<Book> GetAllWithCategory()
        {
            return _context.Books
                .Include(b => b.Category)
                .ToList();
        }

        public async Task<IEnumerable<Book>> GetAllWithCategoryAsync()
        {
            return await _context.Books
                .Include(b => b.Category)
                .ToListAsync();
        }

        public override Book? GetById(int id)
        {
            return _context.Books
                .Include(b => b.Category)
                .FirstOrDefault(b => b.Id == id);
        }

        public override IEnumerable<Book> GetAll()
        {
            return _context.Books
                .Include(b => b.Category)
                .ToList();
        }

        public override void Add(Book book)
        {
            _context.Books.Add(book);
        }

        public override void Update(Book book)
        {
            _context.Books.Update(book);
        }

        public override void Remove(Book book)
        {
            _context.Books.Remove(book);
        }

        public override bool Exists(Expression<Func<Book, bool>> predicate)
        {
            return _context.Books.Any(predicate);
        }
    }
}
