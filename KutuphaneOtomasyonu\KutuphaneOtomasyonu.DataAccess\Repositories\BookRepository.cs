using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class BookRepository : IBookRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public BookRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public IEnumerable<Book> GetAllWithCategory()
        {
            return _context.Books.Include(b => b.Category).ToList();
        }

        public Book? GetById(int id)
        {
            return _context.Books
                .Include(b => b.Category)
                .FirstOrDefault(b => b.Id == id);
        }

        public IEnumerable<Book> GetAll()
        {
            return _context.Books.Include(b => b.Category).ToList();
        }

        public void Add(Book book)
        {
            _context.Books.Add(book);
        }

        public void Update(Book book)
        {
            _context.Books.Update(book);
        }

        public void Remove(Book book)
        {
            _context.Books.Remove(book);
        }

        public bool Exists(System.Linq.Expressions.Expression<System.Func<Book, bool>> predicate)
        {
            return _context.Books.Any(predicate);
        }
    }
}
