using KutuphaneOtomasyonu.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.DataAccess.Repositories
{
    public class BookRepository : IBookRepository
    {
        private readonly KutuphaneOtomasyonuDbContext _context;

        public BookRepository(KutuphaneOtomasyonuDbContext context)
        {
            _context = context;
        }

        public Book? GetById(int id)
        {
            return _context.Books.Find(id);
        }

        public async Task<Book?> GetByIdAsync(int id)
        {
            return await _context.Books.FindAsync(id);
        }

        public IEnumerable<Book> GetAll()
        {
            return _context.Books.ToList();
        }

        public async Task<IEnumerable<Book>> GetAllAsync()
        {
            return await _context.Books.ToListAsync();
        }

        public IEnumerable<Book> Find(Expression<Func<Book, bool>> predicate)
        {
            return _context.Books.Where(predicate).ToList();
        }

        public async Task<IEnumerable<Book>> FindAsync(Expression<Func<Book, bool>> predicate)
        {
            return await _context.Books.Where(predicate).ToListAsync();
        }

        public Book? FirstOrDefault(Expression<Func<Book, bool>> predicate)
        {
            return _context.Books.FirstOrDefault(predicate);
        }

        public async Task<Book?> FirstOrDefaultAsync(Expression<Func<Book, bool>> predicate)
        {
            return await _context.Books.FirstOrDefaultAsync(predicate);
        }

        public void Add(Book book)
        {
            _context.Books.Add(book);
            _context.SaveChanges();
        }

        public async Task AddAsync(Book book)
        {
            await _context.Books.AddAsync(book);
            await _context.SaveChangesAsync();
        }

        public void AddRange(IEnumerable<Book> books)
        {
            _context.Books.AddRange(books);
            _context.SaveChanges();
        }

        public async Task AddRangeAsync(IEnumerable<Book> books)
        {
            await _context.Books.AddRangeAsync(books);
            await _context.SaveChangesAsync();
        }

        public void Update(Book book)
        {
            _context.Books.Update(book);
            _context.SaveChanges();
        }

        public void Remove(Book book)
        {
            _context.Books.Remove(book);
            _context.SaveChanges();
        }

        public void RemoveRange(IEnumerable<Book> books)
        {
            _context.Books.RemoveRange(books);
            _context.SaveChanges();
        }

        public int Count()
        {
            return _context.Books.Count();
        }

        public async Task<int> CountAsync()
        {
            return await _context.Books.CountAsync();
        }

        public int Count(Expression<Func<Book, bool>> predicate)
        {
            return _context.Books.Count(predicate);
        }

        public async Task<int> CountAsync(Expression<Func<Book, bool>> predicate)
        {
            return await _context.Books.CountAsync(predicate);
        }

        public bool Exists(Expression<Func<Book, bool>> predicate)
        {
            return _context.Books.Any(predicate);
        }

        public async Task<bool> ExistsAsync(Expression<Func<Book, bool>> predicate)
        {
            return await _context.Books.AnyAsync(predicate);
        }

        public Book? GetByISBN(string isbn)
        {
            return _context.Books.FirstOrDefault(b => b.ISBN == isbn);
        }

        public async Task<Book?> GetByISBNAsync(string isbn)
        {
            return await _context.Books.FirstOrDefaultAsync(b => b.ISBN == isbn);
        }

        public IEnumerable<Book> GetAllWithCategory()
        {
            return _context.Books.Include(b => b.Category).ToList();
        }

        public async Task<IEnumerable<Book>> GetAllWithCategoryAsync()
        {
            return await _context.Books.Include(b => b.Category).ToListAsync();
        }

        public IEnumerable<Book> GetAvailableBooks()
        {
            return _context.Books.Where(b => !b.Loans.Any(l => !l.ReturnDate.HasValue)).ToList();
        }

        public async Task<IEnumerable<Book>> GetAvailableBooksAsync()
        {
            return await _context.Books.Where(b => !b.Loans.Any(l => !l.ReturnDate.HasValue)).ToListAsync();
        }

        public IEnumerable<Book> SearchBooks(string searchTerm)
        {
            return _context.Books
                .Where(b => b.Title.Contains(searchTerm) || 
                           b.Author.Contains(searchTerm) || 
                           b.ISBN.Contains(searchTerm))
                .ToList();
        }

        public async Task<IEnumerable<Book>> SearchBooksAsync(string searchTerm)
        {
            return await _context.Books
                .Where(b => b.Title.Contains(searchTerm) || 
                           b.Author.Contains(searchTerm) || 
                           b.ISBN.Contains(searchTerm))
                .ToListAsync();
        }
    }
}
