﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KutuphaneOtomasyonu", "KutuphaneOtomasyonu.csproj", "{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KutuphaneOtomasyonu.Entities", "KutuphaneOtomasyonu.Entities\KutuphaneOtomasyonu.Entities.csproj", "{B0B74243-780D-4D75-A446-DC49D6477104}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KutuphaneOtomasyonu.DataAccess", "KutuphaneOtomasyonu.DataAccess\KutuphaneOtomasyonu.DataAccess.csproj", "{67F6D4FF-2F4B-4D57-9460-1A314128580E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KutuphaneOtomasyonu.BusinessLogic", "KutuphaneOtomasyonu.BusinessLogic\KutuphaneOtomasyonu.BusinessLogic.csproj", "{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|x64.Build.0 = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Debug|x86.Build.0 = Debug|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|Any CPU.Build.0 = Release|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|x64.ActiveCfg = Release|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|x64.Build.0 = Release|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|x86.ActiveCfg = Release|Any CPU
		{9258E00B-78FB-4C2F-98C5-5CEB57296BC6}.Release|x86.Build.0 = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|x64.Build.0 = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Debug|x86.Build.0 = Debug|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|x64.ActiveCfg = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|x64.Build.0 = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|x86.ActiveCfg = Release|Any CPU
		{B0B74243-780D-4D75-A446-DC49D6477104}.Release|x86.Build.0 = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|x64.Build.0 = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Debug|x86.Build.0 = Debug|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|Any CPU.Build.0 = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|x64.ActiveCfg = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|x64.Build.0 = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|x86.ActiveCfg = Release|Any CPU
		{67F6D4FF-2F4B-4D57-9460-1A314128580E}.Release|x86.Build.0 = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|x64.Build.0 = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Debug|x86.Build.0 = Debug|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|x64.ActiveCfg = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|x64.Build.0 = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|x86.ActiveCfg = Release|Any CPU
		{CBBCAC0F-FDB4-4B44-963D-2601EEE20A3E}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
