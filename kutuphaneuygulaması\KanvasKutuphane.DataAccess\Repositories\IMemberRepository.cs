using KanvasKutuphane.Entities.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.DataAccess
{
    public interface IMemberRepository
    {
        Task<IEnumerable<Member>> GetAllAsync();
        Task<Member> GetByIdAsync(int id);
        Task AddAsync(Member member);
        Task UpdateAsync(Member member);
        Task DeleteAsync(int id);
        Task<int> CountAsync(System.Linq.Expressions.Expression<System.Func<Member, bool>> predicate);
    }
}
