@model KanvasKutuphane.Entities.Models.Borrowing

@{
    ViewData["Title"] = "Ödün<PERSON> Güncelle";
}

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON> Güncelle</h2>

<form asp-action="Edit" method="post" class="max-w-lg">
    <input type="hidden" asp-for="Id" />

    <div class="mb-4">
        <label asp-for="BorrowDate" class="block font-semibold"><PERSON>ış Tarihi</label>
        <input asp-for="BorrowDate" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="BorrowDate" class="text-red-500 text-sm"></span>
    </div>

    <div class="mb-4">
        <label asp-for="DueDate" class="block font-semibold">Teslim Tarihi</label>
        <input asp-for="DueDate" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="DueDate" class="text-red-500 text-sm"></span>
    </div>

    <div class="mb-4">
        <label asp-for="ReturnDate" class="block font-semibold">İade Tarihi</label>
        <input asp-for="ReturnDate" class="w-full border px-3 py-2 rounded" />
        <span asp-validation-for="ReturnDate" class="text-red-500 text-sm"></span>
    </div>

    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Güncelle</button>
</form>
