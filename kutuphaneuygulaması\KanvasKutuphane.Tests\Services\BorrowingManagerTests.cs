using KanvasKutuphane.Business.Concrete;
using KanvasKutuphane.DataAccess.Repositories;
using KanvasKutuphane.Entities.Models;
using Moq;
using System.Collections.Generic;
using Xunit;

namespace KanvasKutuphane.Tests.Services
{
    public class BorrowingManagerTests
    {
        [Fact]
        public void GetActiveBorrowings_ShouldReturnOnlyUnreturned()
        {
            var mockRepo = new Mock<IBorrowingRepository>();
            mockRepo.Setup(repo => repo.GetActiveBorrowingsAsync())
                .ReturnsAsync(new List<Borrowing> {
                    new Borrowing { Id = 1, ReturnDate = null }
                });

            var manager = new BorrowingManager(mockRepo.Object);
            var result = manager.GetActiveBorrowings();

            Assert.Single(result);
        }
    }
}
