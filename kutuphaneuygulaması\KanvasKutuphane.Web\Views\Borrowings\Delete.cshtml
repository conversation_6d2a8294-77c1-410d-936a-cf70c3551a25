@model KanvasKutuphane.Entities.Models.Borrowing

@{
    ViewData["Title"] = "Ödün<PERSON>";
}

<h2 class="text-2xl font-bold mb-4 text-red-600"><PERSON><PERSON><PERSON><PERSON><PERSON></h2>

<div class="mb-4">
    <p><strong>Üye:</strong> @Model.Member.FirstName @Model.Member.LastName</p>
    <p><strong>Kitap:</strong> @Model.Book.Title</p>
    <p><strong><PERSON><PERSON><PERSON>:</strong> @Model.BorrowDate.ToShortDateString()</p>
    <p><strong><PERSON><PERSON><PERSON>:</strong> @(Model.DueDate.HasValue ? Model.DueDate.Value.ToShortDateString() : "Belirtilmemiş")</p>
</div>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded">Sil</button>
    <a asp-action="Index" class="ml-4 text-blue-600 hover:underline">İptal</a>
</form>
