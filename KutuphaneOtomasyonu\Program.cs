using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using System;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// DbContext
builder.Services.AddDbContext<KutuphaneOtomasyonu.DataAccess.KutuphaneOtomasyonuDbContext>(options =>
{
    options.UseSqlite("Data Source=kutuphane.db");
});

// Identity
builder.Services.AddIdentity<KutuphaneOtomasyonu.Entities.ApplicationUser, Microsoft.AspNetCore.Identity.IdentityRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequiredLength = 6;
})
.AddEntityFrameworkStores<KutuphaneOtomasyonu.DataAccess.KutuphaneOtomasyonuDbContext>()
.AddDefaultTokenProviders();

// Authentication & Authorization
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
    options.ExpireTimeSpan = TimeSpan.FromDays(1);
});

// Repositories & UnitOfWork
builder.Services.AddScoped<KutuphaneOtomasyonu.DataAccess.UnitOfWork.IUnitOfWork, KutuphaneOtomasyonu.DataAccess.UnitOfWork.UnitOfWork>();
builder.Services.AddScoped<KutuphaneOtomasyonu.DataAccess.Repositories.ICategoryRepository, KutuphaneOtomasyonu.DataAccess.Repositories.CategoryRepository>();
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.ICategoryService, KutuphaneOtomasyonu.BusinessLogic.Services.CategoryService>();

// Services
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.IBookService, KutuphaneOtomasyonu.BusinessLogic.Services.BookService>();
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.IMemberService, KutuphaneOtomasyonu.BusinessLogic.Services.MemberService>();
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.ILoanService, KutuphaneOtomasyonu.BusinessLogic.Services.LoanService>();
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.IReportService, KutuphaneOtomasyonu.BusinessLogic.Services.ReportService>();
builder.Services.AddScoped<KutuphaneOtomasyonu.BusinessLogic.Services.ICategoryService, KutuphaneOtomasyonu.BusinessLogic.Services.CategoryService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapStaticAssets();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();

// Veritabanını başlat
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        // Veritabanını silmek için bu satırı etkinleştirdik
       // KutuphaneOtomasyonu.Data.DbInitializer.DeleteDatabase();

        var context = services.GetRequiredService<KutuphaneOtomasyonu.DataAccess.KutuphaneOtomasyonuDbContext>();
        var userManager = services.GetRequiredService<UserManager<KutuphaneOtomasyonu.Entities.ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

        // Asenkron metodu senkron olarak çağırıyoruz
        KutuphaneOtomasyonu.Data.DbInitializer.InitializeAsync(context, userManager, roleManager).Wait();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "Veritabanı başlatılırken bir hata oluştu.");
        Console.WriteLine($"Hata: {ex.Message}");

        if (ex.InnerException != null)
        {
            Console.WriteLine($"İç Hata: {ex.InnerException.Message}");
        }
    }
}

app.Run();
