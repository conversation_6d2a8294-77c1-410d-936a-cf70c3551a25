using System.Collections.Generic;
using System.Threading.Tasks;
using KanvasKutuphane.DataAccess;
using KanvasKutuphane.Entities.Models;
using System.Linq;
using System.Linq.Expressions; // Added for CountAsync predicate

namespace KanvasKutuphane.Business.Services
{
    public class BookManager : IBookService
    {
        private readonly IBookRepository _bookRepository;

        public BookManager(IBookRepository bookRepository)
        {
            _bookRepository = bookRepository;
        }

        public List<Book> GetAll()
        {
            return _bookRepository.GetAllAsync().Result.ToList();
        }

        public Book GetById(int id)
        {
            return _bookRepository.GetByIdAsync(id).Result;
        }

        public void Add(Book book)
        {
            _bookRepository.AddAsync(book).Wait();
        }

        public void Update(Book book)
        {
            _bookRepository.UpdateAsync(book).Wait();
        }

        public void Delete(int id)
        {
            _bookRepository.DeleteAsync(id).Wait();
        }

        public async Task<int> GetTotalBookCountAsync()
        {
            return await _bookRepository.CountAsync();
        }

        public async Task<List<Book>> GetRecentBooksAsync(int count)
        {
            return (await _bookRepository.GetAllAsync())
                   .OrderByDescending(b => b.Id) // Varsayılan olarak Id'ye göre sıralayabiliriz, veya bir CreatedDate alanı varsa ona göre sıralanabilir.
                   .Take(count)
                   .ToList();
        }
    }
}
