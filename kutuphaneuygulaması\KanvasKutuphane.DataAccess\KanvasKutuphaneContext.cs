using KanvasKutuphane.Entities.Models;
using KanvasKutuphane.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;

namespace KanvasKutuphane.DataAccess
{
    public class KanvasKutuphaneContext : IdentityDbContext<AppUser, IdentityRole, string>
    {
        public KanvasKutuphaneContext(DbContextOptions<KanvasKutuphaneContext> options)
            : base(options)
        {
        }

        public DbSet<Book> Books { get; set; }
        public DbSet<Member> Members { get; set; }
        public DbSet<Borrowing> Borrowings { get; set; }
        // Identity tabloları IdentityDbContext tarafından otomatik olarak eklenir.
   
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Book>().Property(b => b.Title).IsRequired().HasMaxLength(200);
            modelBuilder.Entity<Member>().Property(m => m.FirstName).IsRequired().HasMaxLength(50);
            modelBuilder.Entity<Member>().Property(m => m.LastName).IsRequired().HasMaxLength(50);
            modelBuilder.Entity<Borrowing>().HasOne(b => b.Book).WithMany().HasForeignKey(b => b.BookId);
            modelBuilder.Entity<Borrowing>().HasOne(b => b.Member).WithMany().HasForeignKey(b => b.MemberId);
        }
    }
}
