@model KanvasKutuphane.Entities.Models.Book

@{
    ViewData["Title"] = "Kitap Sil";
}

<h2 class="text-2xl font-bold mb-4 text-red-600">Kitap Sil</h2>

<div class="mb-4">
    <p><strong><PERSON><PERSON><PERSON><PERSON><PERSON>:</strong> @Model.Title</p>
    <p><strong>Yazar:</strong> @Model.Author</p>
    <p><strong>Tür:</strong> @Model.Genre</p>
    <p><strong>Stok:</strong> @Model.Stock</p>
</div>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded">Sil</button>
    <a asp-action="Index" class="ml-4 text-blue-600 hover:underline">İptal</a>
</form>
