using KanvasKutuphane.Entities; // AppUser için
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KanvasKutuphane.DataAccess
{
    public interface IUserRepository
    {
        Task<IEnumerable<AppUser>> GetAllAsync();
        Task<AppUser> GetByIdAsync(string id);
        Task<AppUser> GetByEmailAsync(string email);
        Task UpdateAsync(AppUser user);
        Task DeleteAsync(string id);
    }
}
