@model KanvasKutuphane.Entities.Models.Book

@{
    ViewData["Title"] = "Yeni <PERSON>";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-plus me-2 text-success"></i>Yeni <PERSON>
                </h1>
                <p class="text-muted mb-0">Kütüphaneye yeni bir kitap ekleyin</p>
            </div>
            <div>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Geri
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Form Section -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-book me-2"></i>Kitap Bilgileri
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" id="bookForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Title" class="form-label fw-bold">
                                    <i class="fas fa-book me-1"></i>Kitap Başlığı
                                </label>
                                <input asp-for="Title" class="form-control" placeholder="Kitap başlığını girin" />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Author" class="form-label fw-bold">
                                    <i class="fas fa-user-edit me-1"></i>Yazar
                                </label>
                                <input asp-for="Author" class="form-control" placeholder="Yazar adını girin" />
                                <span asp-validation-for="Author" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Genre" class="form-label fw-bold">
                                    <i class="fas fa-tags me-1"></i>Tür/Kategori
                                </label>
                                <select asp-for="Genre" class="form-select" id="genreSelect">
                                    <option value="">Tür seçin</option>
                                    <option value="Roman">Roman</option>
                                    <option value="Bilim Kurgu">Bilim Kurgu</option>
                                    <option value="Tarih">Tarih</option>
                                    <option value="Biyografi">Biyografi</option>
                                    <option value="Şiir">Şiir</option>
                                    <option value="Felsefe">Felsefe</option>
                                    <option value="Bilim">Bilim</option>
                                    <option value="Sanat">Sanat</option>
                                    <option value="Çocuk">Çocuk</option>
                                    <option value="Gençlik">Gençlik</option>
                                    <option value="Diğer">Diğer</option>
                                </select>
                                <div class="mt-2">
                                    <input type="text" class="form-control" id="customGenre" placeholder="Özel tür girin" style="display: none;" />
                                </div>
                                <span asp-validation-for="Genre" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Stock" class="form-label fw-bold">
                                    <i class="fas fa-boxes me-1"></i>Stok Miktarı
                                </label>
                                <input asp-for="Stock" type="number" class="form-control" min="0" max="1000" placeholder="Stok miktarını girin" />
                                <div class="form-text">0-1000 arasında bir değer girin</div>
                                <span asp-validation-for="Stock" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a asp-action="Index" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i>İptal
                                </a>
                                <button type="reset" class="btn btn-warning me-md-2">
                                    <i class="fas fa-undo me-1"></i>Temizle
                                </button>
                                <button type="submit" class="btn btn-save btn-pulse" id="submitBtn">
                                    <i class="fas fa-save me-2"></i>Kitabı Kaydet
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Help Section -->
<div class="row mt-4">
    <div class="col-lg-8 offset-lg-2">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2 text-info"></i>Kitap Ekleme Yardımı
                </h6>
                <ul class="mb-0 small">
                    <li><strong>Başlık:</strong> Kitabın tam adını girin</li>
                    <li><strong>Yazar:</strong> Yazarın adı ve soyadını girin</li>
                    <li><strong>Tür:</strong> Listeden uygun türü seçin veya özel tür girin</li>
                    <li><strong>Stok:</strong> Kütüphanede bulunan kitap sayısını girin</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const genreSelect = document.getElementById('genreSelect');
            const customGenre = document.getElementById('customGenre');
            const form = document.getElementById('bookForm');
            const submitBtn = document.getElementById('submitBtn');

            // Handle custom genre input
            genreSelect.addEventListener('change', function() {
                if (this.value === 'Diğer') {
                    customGenre.style.display = 'block';
                    customGenre.required = true;
                    customGenre.focus();
                } else {
                    customGenre.style.display = 'none';
                    customGenre.required = false;
                    customGenre.value = '';
                }
            });

            // Update genre value when custom input changes
            customGenre.addEventListener('input', function() {
                if (genreSelect.value === 'Diğer') {
                    genreSelect.value = this.value;
                }
            });

            // Form submission handling
            form.addEventListener('submit', function(e) {
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Kaydediliyor...';

                // If custom genre is being used, update the select value
                if (customGenre.style.display !== 'none' && customGenre.value.trim()) {
                    genreSelect.value = customGenre.value.trim();
                }
            });

            // Add input animations
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            console.log('Kitap ekleme formu yüklendi!');
        });
    </script>

    <style>
        .focused {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
}
