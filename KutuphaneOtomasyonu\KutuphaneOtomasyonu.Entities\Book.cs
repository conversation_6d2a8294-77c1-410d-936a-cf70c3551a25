using System.ComponentModel.DataAnnotations;

namespace KutuphaneOtomasyonu.Entities
{
    public class Book
    {
        public int Id { get; set; }
        [Required]
        public string ISBN { get; set; }
        [Required]
        public string Title { get; set; }
        [Required]
        public string Author { get; set; }
        [Required]
        public string Publisher { get; set; }
        [Required]
        public int CategoryId { get; set; }
        public Category Category { get; set; }
        public int Stock { get; set; }

        // Indicates if the book is available for loan
        public bool IsAvailable => Stock > 0;
    }
}