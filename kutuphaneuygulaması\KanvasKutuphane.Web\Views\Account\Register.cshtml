@model KanvasKutuphane.Web.Models.RegisterViewModel

@{
    ViewData["Title"] = "Kayıt Ol";
}

<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12 text-center">
        <h1 class="h2 mb-1">
            <i class="fas fa-user-plus me-2 text-primary"></i>Hesap <PERSON>
        </h1>
        <p class="text-muted mb-0">Kütüphane sistemine kayıt olun</p>
    </div>
</div>

<!-- Registration Form -->
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white text-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-circle me-2"></i><PERSON><PERSON>d<PERSON>
                </h5>
            </div>
            <div class="card-body p-4">
                @if (ViewData.ModelState.ErrorCount > 0)
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Kayıt işlemi başarısız!</strong> Lütfen aşağıdaki hataları düzeltin:
                        <ul class="mb-0 mt-2">
                            @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                            {
                                <li>@error.ErrorMessage</li>
                            }
                        </ul>
                    </div>
                }

                <form asp-action="Register" method="post" id="registerForm" novalidate>
                    <div class="mb-3">
                        <label asp-for="Email" class="form-label fw-bold">
                            <i class="fas fa-envelope me-1"></i>E-posta Adresi
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-at"></i>
                            </span>
                            <input asp-for="Email"
                                   class="form-control"
                                   placeholder="<EMAIL>"
                                   autocomplete="email"
                                   required />
                        </div>
                        <span asp-validation-for="Email" class="text-danger small"></span>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>Geçerli bir e-posta adresi girin
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Password" class="form-label fw-bold">
                            <i class="fas fa-lock me-1"></i>Şifre
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-key"></i>
                            </span>
                            <input asp-for="Password"
                                   type="password"
                                   class="form-control"
                                   placeholder="Güçlü bir şifre oluşturun"
                                   autocomplete="new-password"
                                   required />
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Password" class="text-danger small"></span>
                        <div class="form-text">
                            <i class="fas fa-shield-alt me-1"></i>En az 6 karakter, büyük/küçük harf ve rakam içermeli
                        </div>
                        <!-- Password Strength Indicator -->
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="passwordStrengthText" class="text-muted"></small>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label asp-for="ConfirmPassword" class="form-label fw-bold">
                            <i class="fas fa-lock me-1"></i>Şifre Tekrarı
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-check-double"></i>
                            </span>
                            <input asp-for="ConfirmPassword"
                                   type="password"
                                   class="form-control"
                                   placeholder="Şifrenizi tekrar girin"
                                   autocomplete="new-password"
                                   required />
                            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="text-danger small"></span>
                        <div id="passwordMatch" class="form-text"></div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                <small>
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Kullanım şartlarını</a>
                                    ve <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">gizlilik politikasını</a>
                                    okudum ve kabul ediyorum.
                                </small>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-submit-primary btn-pulse" id="submitBtn">
                            <i class="fas fa-user-plus me-2"></i>Hesap Oluştur
                        </button>
                    </div>
                </form>

                <!-- Login Link -->
                <div class="text-center mt-4">
                    <hr>
                    <p class="mb-0">
                        Zaten hesabınız var mı?
                        <a asp-action="Login" class="text-decoration-none fw-bold">
                            <i class="fas fa-sign-in-alt me-1"></i>Giriş Yapın
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Kullanım Şartları</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Genel Kurallar</h6>
                <p>Kütüphane sistemini kullanırken aşağıdaki kurallara uymanız gerekmektedir:</p>
                <ul>
                    <li>Hesap bilgilerinizi güvenli tutun</li>
                    <li>Sistemi kötüye kullanmayın</li>
                    <li>Diğer kullanıcılara saygılı davranın</li>
                </ul>
                <h6>2. Sorumluluklar</h6>
                <p>Ödünç aldığınız kitapları zamanında iade etmekle yükümlüsünüz.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gizlilik Politikası</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Veri Toplama</h6>
                <p>Sadece kütüphane hizmetleri için gerekli olan kişisel bilgilerinizi topluyoruz.</p>
                <h6>Veri Kullanımı</h6>
                <p>Bilgileriniz sadece kütüphane işlemleri için kullanılır ve üçüncü taraflarla paylaşılmaz.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize form elements safely
            initializeRegisterForm();

            console.log('Kayıt formu yüklendi!');
        });

        function initializeRegisterForm() {
            try {
                const passwordInput = document.getElementById('Password');
                const confirmPasswordInput = document.getElementById('ConfirmPassword');
                const togglePassword = document.getElementById('togglePassword');
                const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
                const passwordStrength = document.getElementById('passwordStrength');
                const passwordStrengthText = document.getElementById('passwordStrengthText');
                const passwordMatch = document.getElementById('passwordMatch');
                const form = document.getElementById('registerForm');
                const submitBtn = document.getElementById('submitBtn');

                // Check if all elements exist before adding event listeners
                if (!passwordInput || !confirmPasswordInput || !form || !submitBtn) {
                    console.warn('Some form elements not found');
                    return;
                }

                // Password visibility toggle
                if (togglePassword) {
                    togglePassword.addEventListener('click', function() {
                        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                        passwordInput.setAttribute('type', type);
                        const icon = this.querySelector('i');
                        if (icon) {
                            icon.classList.toggle('fa-eye');
                            icon.classList.toggle('fa-eye-slash');
                        }
                    });
                }

                if (toggleConfirmPassword) {
                    toggleConfirmPassword.addEventListener('click', function() {
                        const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                        confirmPasswordInput.setAttribute('type', type);
                        const icon = this.querySelector('i');
                        if (icon) {
                            icon.classList.toggle('fa-eye');
                            icon.classList.toggle('fa-eye-slash');
                        }
                    });
                }

                // Password strength checker
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let strengthText = '';
                    let strengthClass = '';

                    if (password.length >= 6) strength += 25;
                    if (password.match(/[a-z]/)) strength += 25;
                    if (password.match(/[A-Z]/)) strength += 25;
                    if (password.match(/[0-9]/)) strength += 25;

                    if (strength < 50) {
                        strengthText = 'Zayıf';
                        strengthClass = 'bg-danger';
                    } else if (strength < 75) {
                        strengthText = 'Orta';
                        strengthClass = 'bg-warning';
                    } else {
                        strengthText = 'Güçlü';
                        strengthClass = 'bg-success';
                    }

                    if (passwordStrength) {
                        passwordStrength.style.width = strength + '%';
                        passwordStrength.className = 'progress-bar ' + strengthClass;
                    }

                    if (passwordStrengthText) {
                        passwordStrengthText.textContent = strengthText;
                    }

                    checkPasswordMatch();
                });

                // Password match checker
                confirmPasswordInput.addEventListener('input', checkPasswordMatch);

                function checkPasswordMatch() {
                    const password = passwordInput.value;
                    const confirmPassword = confirmPasswordInput.value;

                    if (passwordMatch && confirmPassword.length > 0) {
                        if (password === confirmPassword) {
                            passwordMatch.innerHTML = '<i class="fas fa-check text-success me-1"></i><span class="text-success">Şifreler eşleşiyor</span>';
                            confirmPasswordInput.classList.remove('is-invalid');
                            confirmPasswordInput.classList.add('is-valid');
                        } else {
                            passwordMatch.innerHTML = '<i class="fas fa-times text-danger me-1"></i><span class="text-danger">Şifreler eşleşmiyor</span>';
                            confirmPasswordInput.classList.remove('is-valid');
                            confirmPasswordInput.classList.add('is-invalid');
                        }
                    } else if (passwordMatch) {
                        passwordMatch.innerHTML = '';
                        confirmPasswordInput.classList.remove('is-valid', 'is-invalid');
                    }
                }

                // Form submission with loading state
                form.addEventListener('submit', function(e) {
                    // Add loading state to submit button
                    submitBtn.disabled = true;
                    submitBtn.classList.add('btn-loading');

                    // Reset button after 10 seconds as fallback
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('btn-loading');
                    }, 10000);
                });

                // Input focus animations
                const inputs = form.querySelectorAll('input');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        const container = this.closest('.input-group, .form-group, .mb-3');
                        if (container) {
                            container.classList.add('focused');
                        }
                    });

                    input.addEventListener('blur', function() {
                        const container = this.closest('.input-group, .form-group, .mb-3');
                        if (container) {
                            container.classList.remove('focused');
                        }
                    });
                });

                // Initialize form validation if available
                if (typeof $ !== 'undefined' && $.validator) {
                    try {
                        $(form).validate({
                            errorClass: 'is-invalid',
                            validClass: 'is-valid',
                            errorElement: 'div',
                            errorPlacement: function(error, element) {
                                error.addClass('invalid-feedback');
                                element.closest('.mb-3').append(error);
                            }
                        });
                    } catch (validationError) {
                        console.warn('jQuery validation not available:', validationError);
                    }
                }

            } catch (error) {
                console.error('Error initializing register form:', error);
            }
        }
    </script>

    <!-- Load validation scripts after our custom script -->
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <style>
        .focused {
            transform: scale(1.01);
            transition: transform 0.2s ease;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .card {
            transition: all 0.3s ease;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .progress {
            border-radius: 10px;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }

        /* Enhanced button loading state */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Error handling */
        .invalid-feedback {
            display: block;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .is-valid {
            border-color: #198754;
        }
    </style>
}
