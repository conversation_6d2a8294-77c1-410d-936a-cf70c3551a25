@model KanvasKutuphane.Web.Models.RegisterViewModel

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON>l</h2>

<form asp-action="Register" method="post" class="max-w-md">
    <div class="mb-4">
        <label asp-for="Email" class="block font-semibold">E-posta</label>
        <input asp-for="Email" class="w-full border rounded px-3 py-2" />
        <span asp-validation-for="Email" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="Password" class="block font-semibold">Şifre</label>
        <input asp-for="Password" class="w-full border rounded px-3 py-2" />
        <span asp-validation-for="Password" class="text-red-500 text-sm"></span>
    </div>
    <div class="mb-4">
        <label asp-for="ConfirmPassword" class="block font-semibold">Şifre Tekrar</label>
        <input asp-for="ConfirmPassword" class="w-full border rounded px-3 py-2" />
        <span asp-validation-for="ConfirmPassword" class="text-red-500 text-sm"></span>
    </div>
    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded">Kayıt Ol</button>
</form>
