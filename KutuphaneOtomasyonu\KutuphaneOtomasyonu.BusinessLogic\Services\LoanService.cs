using KutuphaneOtomasyonu.BusinessLogic.Models;
using KutuphaneOtomasyonu.DataAccess.Repositories;
using KutuphaneOtomasyonu.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KutuphaneOtomasyonu.BusinessLogic.Services
{
    public class LoanService : ILoanService
    {
        private readonly ILoanRepository _loanRepository;
        private readonly IBookRepository _bookRepository;
        private readonly IMemberRepository _memberRepository;

        public LoanService(
            ILoanRepository loanRepository,
            IBookRepository bookRepository,
            IMemberRepository memberRepository)
        {
            _loanRepository = loanRepository;
            _bookRepository = bookRepository;
            _memberRepository = memberRepository;
        }

        public Loan GetLoanById(int id)
        {
            return _loanRepository.GetById(id);
        }

        public async Task<Loan> GetLoanByIdAsync(int id)
        {
            return await _loanRepository.GetByIdAsync(id);
        }

        public IEnumerable<Loan> GetAllLoans()
        {
            return _loanRepository.GetAll();
        }

        public async Task<IEnumerable<Loan>> GetAllLoansAsync()
        {
            return await _loanRepository.GetAllAsync();
        }

        public IEnumerable<Loan> GetActiveLoans()
        {
            return _loanRepository.Find(l => !l.ReturnDate.HasValue);
        }

        public async Task<IEnumerable<Loan>> GetActiveLoansAsync()
        {
            return await _loanRepository.FindAsync(l => !l.ReturnDate.HasValue);
        }

        public IEnumerable<Loan> GetOverdueLoans()
        {
            var today = DateTime.Today;
            return _loanRepository.Find(l => !l.ReturnDate.HasValue && l.DueDate < today);
        }

        public async Task<IEnumerable<Loan>> GetOverdueLoansAsync()
        {
            var today = DateTime.Today;
            return await _loanRepository.FindAsync(l => !l.ReturnDate.HasValue && l.DueDate < today);
        }

        public IEnumerable<Loan> GetLoansByMember(int memberId)
        {
            return _loanRepository.Find(l => l.MemberId == memberId);
        }

        public async Task<IEnumerable<Loan>> GetLoansByMemberAsync(int memberId)
        {
            return await _loanRepository.FindAsync(l => l.MemberId == memberId);
        }

        public IEnumerable<Loan> GetLoansByBook(int bookId)
        {
            return _loanRepository.Find(l => l.BookId == bookId);
        }

        public async Task<IEnumerable<Loan>> GetLoansByBookAsync(int bookId)
        {
            return await _loanRepository.FindAsync(l => l.BookId == bookId);
        }

        public void LoanBook(int bookId, int memberId, int loanDays = 15)
        {
            var book = _bookRepository.GetById(bookId);
            var member = _memberRepository.GetById(memberId);

            if (book == null || member == null)
                throw new ArgumentException("Book or member not found");

            if (!book.IsAvailable)
                throw new InvalidOperationException("Book is not available for loan");

            var loan = new Loan
            {
                BookId = bookId,
                MemberId = memberId,
                LoanDate = DateTime.Today,
                DueDate = DateTime.Today.AddDays(loanDays)
            };

            _loanRepository.Add(loan);
            book.IsAvailable = false;
            _bookRepository.Update(book);
        }

        public async Task LoanBookAsync(int bookId, int memberId, int loanDays = 15)
        {
            var book = await _bookRepository.GetByIdAsync(bookId);
            var member = await _memberRepository.GetByIdAsync(memberId);

            if (book == null || member == null)
                throw new ArgumentException("Book or member not found");

            if (!book.IsAvailable)
                throw new InvalidOperationException("Book is not available for loan");

            var loan = new Loan
            {
                BookId = bookId,
                MemberId = memberId,
                LoanDate = DateTime.Today,
                DueDate = DateTime.Today.AddDays(loanDays)
            };

            await _loanRepository.AddAsync(loan);
            book.IsAvailable = false;
            await _bookRepository.UpdateAsync(book);
        }

        public void ReturnBook(int loanId)
        {
            var loan = _loanRepository.GetById(loanId);
            if (loan == null)
                throw new ArgumentException("Loan not found");

            if (loan.ReturnDate.HasValue)
                throw new InvalidOperationException("Book already returned");

            loan.ReturnDate = DateTime.Today;
            _loanRepository.Update(loan);

            var book = _bookRepository.GetById(loan.BookId);
            book.IsAvailable = true;
            _bookRepository.Update(book);
        }

        public async Task ReturnBookAsync(int loanId)
        {
            var loan = await _loanRepository.GetByIdAsync(loanId);
            if (loan == null)
                throw new ArgumentException("Loan not found");

            if (loan.ReturnDate.HasValue)
                throw new InvalidOperationException("Book already returned");

            loan.ReturnDate = DateTime.Today;
            await _loanRepository.UpdateAsync(loan);

            var book = await _bookRepository.GetByIdAsync(loan.BookId);
            book.IsAvailable = true;
            await _bookRepository.UpdateAsync(book);
        }

        public void ExtendLoan(int loanId, int additionalDays)
        {
            var loan = _loanRepository.GetById(loanId);
            if (loan == null)
                throw new ArgumentException("Loan not found");

            if (loan.ReturnDate.HasValue)
                throw new InvalidOperationException("Book already returned");

            if (loan.DueDate < DateTime.Today)
                throw new InvalidOperationException("Loan is overdue and cannot be extended");

            loan.DueDate = loan.DueDate.AddDays(additionalDays);
            _loanRepository.Update(loan);
        }

        public async Task ExtendLoanAsync(int loanId, int additionalDays)
        {
            var loan = await _loanRepository.GetByIdAsync(loanId);
            if (loan == null)
                throw new ArgumentException("Loan not found");

            if (loan.ReturnDate.HasValue)
                throw new InvalidOperationException("Book already returned");

            if (loan.DueDate < DateTime.Today)
                throw new InvalidOperationException("Loan is overdue and cannot be extended");

            loan.DueDate = loan.DueDate.AddDays(additionalDays);
            await _loanRepository.UpdateAsync(loan);
        }

        public int GetActiveLoansCount()
        {
            return _loanRepository.Find(l => !l.ReturnDate.HasValue).Count();
        }

        public async Task<int> GetActiveLoansCountAsync()
        {
            var activeLoans = await _loanRepository.FindAsync(l => !l.ReturnDate.HasValue);
            return activeLoans.Count();
        }

        public int GetOverdueLoansCount()
        {
            var today = DateTime.Today;
            return _loanRepository.Find(l => !l.ReturnDate.HasValue && l.DueDate < today).Count();
        }

        public async Task<int> GetOverdueLoansCountAsync()
        {
            var today = DateTime.Today;
            var overdueLoans = await _loanRepository.FindAsync(l => !l.ReturnDate.HasValue && l.DueDate < today);
            return overdueLoans.Count();
        }
    }
}
