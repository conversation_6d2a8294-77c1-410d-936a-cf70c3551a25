using System;

namespace KanvasKutuphane.Business.ViewModels
{
    public class LateReturnViewModel
    {
        public string BookTitle { get; set; } = string.Empty;
        public string MemberName { get; set; } = string.Empty;
        public DateTime DueDate { get; set; }
        public int DaysLate { get; set; }

        // Calculated property for display
        public string DaysLateText => DaysLate == 1 ? "1 gün" : $"{DaysLate} gün";

        // Status based on days late
        public string StatusClass => DaysLate switch
        {
            <= 3 => "warning",
            <= 7 => "danger",
            _ => "dark"
        };
    }
}