@model KanvasKutuphane.Entities.Models.Member
@{
    ViewData["Title"] = "Üye Detayları";
}

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i><PERSON>ye Detayları
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">
                                <i class="fas fa-id-card me-1"></i>Üye No:
                            </dt>
                            <dd class="col-sm-8"><strong>@Model.Id</strong></dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-user me-1"></i>Ad:
                            </dt>
                            <dd class="col-sm-8">@Model.FirstName</dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-user me-1"></i>Soyad:
                            </dt>
                            <dd class="col-sm-8">@Model.LastName</dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-envelope me-1"></i>E-posta:
                            </dt>
                            <dd class="col-sm-8">@Model.Email</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">
                                <i class="fas fa-phone me-1"></i>Telefon:
                            </dt>
                            <dd class="col-sm-8">@Model.Phone</dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-calendar me-1"></i>Katılım Tarihi:
                            </dt>
                            <dd class="col-sm-8">
                                @Model.JoinDate.ToString("dd.MM.yyyy")
                            </dd>

                            <dt class="col-sm-4">
                                <i class="fas fa-info-circle me-1"></i>Durum:
                            </dt>
                            <dd class="col-sm-8">
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">Aktif Üye</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Pasif Üye</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="d-flex gap-2 flex-wrap">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Geri
                        </a>
                        
                        @if (User.IsInRole("Admin"))
                        {
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Düzenle
                            </a>
                            
                            <a asp-controller="Borrowings" asp-action="Create" asp-route-memberId="@Model.Id" class="btn btn-success">
                                <i class="fas fa-hand-holding me-1"></i>Kitap Ödünç Ver
                            </a>
                            
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Sil
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Üye İstatistikleri
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-3x text-success"></i>
                    </div>
                    <h6>@Model.FirstName @Model.LastName</h6>
                    <p class="text-muted small">
                        Bu üye @Model.JoinDate.ToString("dd.MM.yyyy") tarihinde sisteme katılmıştır.
                        Üye bilgilerini yukarıda görebilirsiniz.
                    </p>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Ödünç geçmişi ve detaylı istatistikler için raporlar bölümünü ziyaret edin.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
