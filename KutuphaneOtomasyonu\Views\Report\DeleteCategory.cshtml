@model KutuphaneOtomasyonu.Entities.Category
@{
    ViewData["Title"] = "Kategori Sil";
}
<div class="card">
    <div class="card-header bg-danger text-white">
        <h4 class="mb-0"><PERSON><PERSON><PERSON></h4>
    </div>
    <div class="card-body">
        <h5 class="card-title mb-4">Bu kategoriyi silmek istediğinizden emin misiniz?</h5>
        <dl class="row">
            <dt class="col-sm-3">Kategori Adı:</dt>
            <dd class="col-sm-9">@Model.Name</dd>
        </dl>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            Bu kategoriyi sildiğinizde, bu kategoriye ait tüm kitapların kategori bilgisi kaldırılacaktır.
        </div>
        <form asp-action="DeleteCategory" method="post" class="mt-4">
            <input type="hidden" asp-for="Id" />
            <div class="d-flex justify-content-between">
                <a asp-action="Categories" class="btn btn-secondary">Vazgeç</a>
                <button type="submit" class="btn btn-danger">Sil</button>
            </div>
        </form>
    </div>
</div>